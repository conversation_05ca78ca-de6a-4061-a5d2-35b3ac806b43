import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ServiceRepositoryPort } from '../../../ports/repositories/service-repository.port';

@Injectable()
export class DeleteServiceUseCase {
  constructor(
    @Inject('SERVICE_REPOSITORY')
    private readonly serviceRepository: ServiceRepositoryPort,
  ) {}

  async execute(id: string): Promise<void> {
    const service = await this.serviceRepository.findById(id);
    
    if (!service) {
      throw new NotFoundException(`Serviço com ID ${id} não encontrado`);
    }

    await this.serviceRepository.delete(id);
  }
} 