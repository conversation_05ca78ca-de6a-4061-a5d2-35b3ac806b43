export interface Otp {
    id: string;
    userId: string;
    hash: string;
    expiresAt: Date;
    createdAt: Date;
}

export interface UsersOtpRepository {
    createOtp(userId: string, hash: string, expiresAt: Date): Promise<void>;
    findOtpByUserId(userId: string): Promise<Otp[]>;
    deleteOtp(id: string): Promise<void>;
    deleteOtpByUserId(userId: string): Promise<void>;
    deleteExpiredOtps(): Promise<void>;
}