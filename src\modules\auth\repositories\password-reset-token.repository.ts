import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../infrastructure/prisma/prisma.service';
import {
  PasswordResetToken,
  PasswordResetTokenRepository,
} from '../interfaces/password-reset-token.repository.interface';

@Injectable()
export class PrismaPasswordResetTokenRepository
  implements PasswordResetTokenRepository
{
  constructor(private prisma: PrismaService) {}

  async create(
    data: Omit<PasswordResetToken, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<PasswordResetToken> {
    return this.prisma.$queryRaw`
      INSERT INTO "core"."password_reset_tokens" (id, "userId", token, "expiresAt", used, "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${data.userId}, ${data.token}, ${data.expiresAt}, ${data.used}, NOW(), NOW())
      RETURNING *
    `;
  }

  async findByToken(token: string): Promise<PasswordResetToken | null> {
    const result = await this.prisma.$queryRaw<PasswordResetToken[]>`
      SELECT * FROM "core"."password_reset_tokens"
      WHERE token = ${token}
      AND used = false
      AND "expiresAt" > NOW()
      LIMIT 1
    `;
    return result[0] || null;
  }

  async markAsUsed(token: string): Promise<void> {
    await this.prisma.$queryRaw`
      UPDATE "core"."password_reset_tokens"
      SET used = true, "updatedAt" = NOW()
      WHERE token = ${token}
    `;
  }

  async deleteExpiredTokens(): Promise<void> {
    await this.prisma.$queryRaw`
      DELETE FROM "core"."password_reset_tokens"
      WHERE "expiresAt" < NOW() OR used = true
    `;
  }

  async deleteByToken(token: string): Promise<void> {
    await this.prisma.$queryRaw`
      DELETE FROM "core"."password_reset_tokens"
      WHERE token = ${token}
    `;
  }

  async deleteByUserId(userId: string): Promise<void> {
    await this.prisma.$queryRaw`
      DELETE FROM "core"."password_reset_tokens"
      WHERE "userId" = ${userId}
    `;
  }
}
