import { ApiProperty } from '@nestjs/swagger';
import { CostCenterResponseDto } from './cost-center-response.dto';

export class PaginatedCostCentersResponseDto {
  @ApiProperty({
    description: 'List of cost centers',
    type: [CostCenterResponseDto],
  })
  items: CostCenterResponseDto[];

  @ApiProperty({
    description: 'Total number of cost centers',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of cost centers per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Number of cost centers to skip',
    example: 0,
  })
  offset: number;
}
