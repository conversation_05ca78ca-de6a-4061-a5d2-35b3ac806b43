import { SupplierCreatedEvent } from './supplier-created.event';
import { DomainEvent } from '../../../../core/domain/events/domain-event';

describe('SupplierCreatedEvent', () => {
  const createMockPayload = () => ({
    supplierId: 'test-supplier-id',
    name: 'Test Supplier',
    cnpj: '12345678901234',
    createdAt: new Date('2024-03-20T10:00:00Z'),
  });

  it('should create an event with the correct name', () => {
    const event = new SupplierCreatedEvent(createMockPayload());
    expect((event as DomainEvent).eventName).toBe('supplier.created');
  });

  it('should store the payload correctly', () => {
    const mockPayload = createMockPayload();
    const event = new SupplierCreatedEvent(mockPayload);
    expect(event.payload).toEqual(mockPayload);
  });

  it('should format the data correctly', () => {
    const mockPayload = createMockPayload();
    const event = new SupplierCreatedEvent(mockPayload);
    const data = event.getData();

    expect(data).toEqual({
      supplierId: mockPayload.supplierId,
      name: mockPayload.name,
      cnpj: mockPayload.cnpj,
      createdAt: mockPayload.createdAt.toISOString(),
    });
  });

  it('should convert createdAt to ISO string in getData', () => {
    const mockPayload = createMockPayload();
    const event = new SupplierCreatedEvent(mockPayload);
    const data = event.getData() as {
      supplierId: string;
      name: string;
      cnpj: string;
      createdAt: string;
    };

    expect(data.createdAt).toBe('2024-03-20T10:00:00.000Z');
  });

  it('should maintain immutable payload properties', () => {
    const mockPayload = createMockPayload();
    const event = new SupplierCreatedEvent(mockPayload);
    const originalPayload = { ...mockPayload };

    // Criar um novo objeto para modificar
    const modifiedPayload = { ...mockPayload };
    modifiedPayload.name = 'Changed Name';
    modifiedPayload.cnpj = '98765432109876';

    expect(event.payload).toEqual(originalPayload);
    expect(event.payload).not.toEqual(modifiedPayload);
  });

  it('should serialize the event correctly', () => {
    const mockPayload = createMockPayload();
    const event = new SupplierCreatedEvent(mockPayload);
    const serialized = event.serialize() as {
      eventId: string;
      eventName: string;
      occurredAt: string;
      data: {
        supplierId: string;
        name: string;
        cnpj: string;
        createdAt: string;
      };
    };

    expect(serialized.eventName).toBe('supplier.created');
    expect(serialized.eventId).toBeDefined();
    expect(serialized.occurredAt).toBeDefined();
    expect(serialized.data).toEqual({
      supplierId: mockPayload.supplierId,
      name: mockPayload.name,
      cnpj: mockPayload.cnpj,
      createdAt: mockPayload.createdAt.toISOString(),
    });
  });
});
