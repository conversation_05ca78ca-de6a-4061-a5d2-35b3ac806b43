import { Injectable, Inject } from '@nestjs/common';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { EntityType } from '../../../domain/service/enums/entity-type.enum';
import { EntityType as DocumentEntityType } from '../../../../modules/documents/domain/enums/entity-type.enum';
import { DocumentStatus } from '../../../../modules/documents/domain/enums/document-status.enum';

@Injectable()
export class ValidateSupplierActivationUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
    private readonly prisma: PrismaService,
  ) {}

  async execute(supplierUuid: string, updatedBy: string): Promise<boolean> {
    console.log(`[ValidateSupplierActivation] Iniciando validação para supplier UUID: ${supplierUuid}`);
    
    // Buscar o supplier
    const supplier = await this.supplierRepository.findById(supplierUuid);
    if (!supplier) {
      console.error(`[ValidateSupplierActivation] Supplier não encontrado: ${supplierUuid}`);
      throw new Error('Supplier not found');
    }

    console.log(`[ValidateSupplierActivation] Supplier encontrado. Status atual: ${supplier.status}`);

    // Se já está ACTIVE, não precisa validar
    if (supplier.status === SupplierStatus.ACTIVE) {
      console.log(`[ValidateSupplierActivation] Supplier já está ACTIVE`);
      return true;
    }

    console.log(`[ValidateSupplierActivation] Verificando requisitos...`);

    // Verificar se tem pelo menos um contrato
    console.log(`[ValidateSupplierActivation] Buscando contratos com entityType: SUPPLIER, entityUuid: ${supplierUuid}`);
    const contractsCount = await this.prisma.contract.count({
      where: {
        entityType: 'SUPPLIER',
        entityUuid: supplierUuid,
      },
    });
    console.log(`[ValidateSupplierActivation] Contratos encontrados: ${contractsCount}`);

    // Verificar se tem pelo menos um documento
    console.log(`[ValidateSupplierActivation] Buscando documentos com entityType: ${DocumentEntityType.SUPPLIER}, entityUuid: ${supplierUuid}, status: ${DocumentStatus.ACTIVE}`);
    const documentsCount = await this.prisma.document.count({
      where: {
        entityType: DocumentEntityType.SUPPLIER,
        entityUuid: supplierUuid,
        status: DocumentStatus.ACTIVE,
      },
    });
    console.log(`[ValidateSupplierActivation] Documentos ativos encontrados: ${documentsCount}`);

    // Verificar se tem pelo menos um serviço
    console.log(`[ValidateSupplierActivation] Buscando serviços com entityType: ${EntityType.SUPPLIER}, entityUuid: ${supplierUuid}`);
    const servicesCount = await this.prisma.service.count({
      where: {
        entityType: EntityType.SUPPLIER,
        entityUuid: supplierUuid,
        deletedAt: null,
      },
    });
    console.log(`[ValidateSupplierActivation] Serviços encontrados: ${servicesCount}`);

    // Verificar se tem pelo menos um contato
    console.log(`[ValidateSupplierActivation] Buscando contatos com supplierId: ${supplierUuid}`);
    const contactsCount = await this.prisma.supplierContact.count({
      where: {
        supplierId: supplierUuid,
        deletedAt: null,
      },
    });
    console.log(`[ValidateSupplierActivation] Contatos encontrados: ${contactsCount}`);

    // Se tem todos os requisitos, ativar o supplier
    const hasAllRequirements = 
      contractsCount > 0 && 
      documentsCount > 0 && 
      servicesCount > 0 && 
      contactsCount > 0;

    console.log(`[ValidateSupplierActivation] Resumo dos requisitos:`, {
      hasContract: contractsCount > 0,
      hasDocument: documentsCount > 0,
      hasService: servicesCount > 0,
      hasContact: contactsCount > 0,
      hasAllRequirements,
      currentStatus: supplier.status
    });

    if (hasAllRequirements && supplier.status === SupplierStatus.PENDING) {
      console.log(`[ValidateSupplierActivation] Todos os requisitos atendidos. Ativando supplier...`);
      supplier.updateStatus(SupplierStatus.ACTIVE, updatedBy);
      await this.supplierRepository.update(supplier);
      console.log(`[ValidateSupplierActivation] Supplier ativado com sucesso!`);
      return true;
    }

    console.log(`[ValidateSupplierActivation] Supplier não pode ser ativado. Requisitos não atendidos ou status inválido.`);
    return false;
  }

  async validateRequirements(supplierUuid: string): Promise<{
    hasContract: boolean;
    hasDocument: boolean;
    hasService: boolean;
    hasContact: boolean;
    canActivate: boolean;
    currentStatus: SupplierStatus;
  }> {
    console.log(`[ValidateSupplierActivation] Validando requisitos para supplier UUID: ${supplierUuid}`);
    
    const supplier = await this.supplierRepository.findById(supplierUuid);
    if (!supplier) {
      console.error(`[ValidateSupplierActivation] Supplier não encontrado: ${supplierUuid}`);
      throw new Error('Supplier not found');
    }

    console.log(`[ValidateSupplierActivation] Supplier encontrado. Status: ${supplier.status}`);

    const [contractsCount, documentsCount, servicesCount, contactsCount] = await Promise.all([
      this.prisma.contract.count({
        where: {
          entityType: 'SUPPLIER',
          entityUuid: supplierUuid,
        },
      }),
      this.prisma.document.count({
        where: {
          entityType: DocumentEntityType.SUPPLIER,
          entityUuid: supplierUuid,
          status: DocumentStatus.ACTIVE,
        },
      }),
      this.prisma.service.count({
        where: {
          entityType: EntityType.SUPPLIER,
          entityUuid: supplierUuid,
          deletedAt: null,
        },
      }),
      this.prisma.supplierContact.count({
        where: {
          supplierId: supplierUuid,
          deletedAt: null,
        },
      }),
    ]);

    console.log(`[ValidateSupplierActivation] Contagem de requisitos:`, {
      contracts: contractsCount,
      documents: documentsCount,
      services: servicesCount,
      contacts: contactsCount
    });

    const hasContract = contractsCount > 0;
    const hasDocument = documentsCount > 0;
    const hasService = servicesCount > 0;
    const hasContact = contactsCount > 0;
    const canActivate = hasContract && hasDocument && hasService && hasContact;

    const result = {
      hasContract,
      hasDocument,
      hasService,
      hasContact,
      canActivate,
      currentStatus: supplier.status,
    };

    console.log(`[ValidateSupplierActivation] Resultado da validação:`, result);

    return result;
  }
} 