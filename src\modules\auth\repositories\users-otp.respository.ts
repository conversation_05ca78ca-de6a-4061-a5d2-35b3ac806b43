import { Injectable } from "@nestjs/common";
import { Otp, UsersOtpRepository } from "../interfaces/users-otp.repository.interface";
import { PrismaService } from "@/infrastructure/prisma/prisma.service";


@Injectable()
export class PrismaUsersOtpRepository implements UsersOtpRepository {
    constructor(private prisma: PrismaService) {}

  async createOtp(userId: string, hash: string, expiresAt: Date): Promise<void> {
    await this.prisma.$queryRaw`
        INSERT INTO core.users_otp
            (id, "userId", hash, "expiresAt", "createdAt")
            VALUES(gen_random_uuid(), ${userId}, ${hash}, ${expiresAt}, NOW())
    `;
  }

  async findOtpByUserId(userId: string): Promise<Otp[]> {
    const result = await this.prisma.$queryRaw<Otp[]>`
      SELECT * FROM "core"."users_otp"
      WHERE "userId" = ${userId}
    `;
    return result || [];
  }
  
  async deleteOtp(id: string): Promise<void> {
    await this.prisma.$queryRaw`
        DELETE FROM "core"."users_otp"
        WHERE id = ${id}
    `;
  }
  
  async deleteOtpByUserId(userId: string): Promise<void> {
    await this.prisma.$queryRaw`
      DELETE FROM "core"."users_otp"
      WHERE "userId" = ${userId}
    `;
  }
  
  async deleteExpiredOtps(): Promise<void> {
    await this.prisma.$queryRaw`
      DELETE FROM "core"."users_otp"
      WHERE "expiresAt" < NOW()
    `;
  }
}