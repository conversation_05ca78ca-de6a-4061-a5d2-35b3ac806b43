import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';
import { UpdateCustomerPaymentPreferenceDto } from '../../infrastructure/dtos/customer-payment-preference.dto';
import { CustomerPaymentPreference } from '../../domain/entities/customer-payment-preference.entity';

@Injectable()
export class UpdateCustomerPaymentPreferenceUseCase {
  constructor(
    @Inject('ICustomerPaymentPreferenceRepository')
    private readonly repository: ICustomerPaymentPreferenceRepository,
  ) {}

  async execute(id: string, dto: UpdateCustomerPaymentPreferenceDto): Promise<CustomerPaymentPreference> {
    const preference = await this.repository.findById(id);
    if (!preference) {
      throw new NotFoundException('Customer payment preference not found');
    }
    return this.repository.update(id, {
      ...dto,
      updatedAt: new Date(),
    });
  }
} 