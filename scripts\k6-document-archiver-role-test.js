import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  vus: 5,
  duration: '30s',
};

const BASE_URL = 'http://localhost:3000/v1/backoffice';

export default function () {
  const email = `k6docarch${__VU}_${__ITER}@test.com`;
  const registerPayload = JSON.stringify({
    name: 'Doc Archiver K6',
    email: email,
    password: 'Senha123!',
  });

  let registerRes = http.post(`${BASE_URL}/auth/register`, registerPayload, {
    headers: { 'Content-Type': 'application/json' },
  });
  check(registerRes, { 'register status 201': (r) => r.status === 201 });

  // Aqui seria necessário atribuir a role DOCUMENT_ARCHIVER via API/admin, se possível
  sleep(0.5);

  const loginPayload = JSON.stringify({
    username: email,
    password: 'Senha123!',
  });

  let loginRes = http.post(`${BASE_URL}/auth/login`, loginPayload, {
    headers: { 'Content-Type': 'application/json' },
  });
  check(loginRes, {
    'login status 201': (r) => r.status === 201,
    'login has token': (r) => r.json('access_token') !== undefined,
  });
  const token = loginRes.json('access_token');

  // Testar endpoint DOCUMENT_ARCHIVER
  let archiveRes = http.get(`${BASE_URL}/documents/archive`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  check(archiveRes, { 'archive endpoint status 200': (r) => r.status === 200 || r.status === 404 });

  sleep(1);
} 