import { ApiProperty } from '@nestjs/swagger';

export class LabelResponseDto {
  @ApiProperty({ example: 1, description: 'ID da label' })
  id: number;

  @ApiProperty({ example: 'Financeiro', description: 'Nome da label' })
  label: string;

  @ApiProperty({
    example: 'metodtoPgto',
    description: 'ID do componente associado',
  })
  idComponent: string;

  @ApiProperty({ example: 'finance', description: 'Nome do módulo associado' })
  modulo: string;

  @ApiProperty({ example: '2024-06-14T12:00:00Z', description: 'Data de criação' })
  createdAt: Date;

  @ApiProperty({ example: '2024-06-14T12:00:00Z', description: 'Data de atualização' })
  updatedAt: Date;
} 