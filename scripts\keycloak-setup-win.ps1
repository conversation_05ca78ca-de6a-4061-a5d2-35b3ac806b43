#requires -Version 5.0

$ErrorActionPreference = 'Stop'

# Configurações
$KEYCLOAK_URL = "http://localhost:8080"
$REALM = "master"
$ADMIN_USER = "admin"
$ADMIN_PASS = "admin"
$global:CLIENT_ID = "backend-dev-client"
$CLIENT_SECRET = "myclientsecret"
$TEST_USER = "testuser"
$TEST_PASS = "testpass"
$ROLES = @(
  "ADMIN"
  "USER"
  "FINANCE_ADMIN"
  "FINANCE_USER"
  "DOCUMENT_ARCHIVER"
  "DOCUMENT_UPLOADER"
  "DOCUMENT_VIEWER"
)

function Get-AdminToken {
  $response = Invoke-RestMethod -Method Post -Uri "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" -Body @{
    grant_type = 'password'
    client_id = 'admin-cli'
    username = $ADMIN_USER
    password = $ADMIN_PASS
  } -ContentType 'application/x-www-form-urlencoded'
  return $response.access_token
}

function Ensure-RealmExists {
  param($token)
  $exists = (Invoke-WebRequest -Uri "$KEYCLOAK_URL/admin/realms/$REALM" -Headers @{Authorization = "Bearer $token"} -Method Get -UseBasicParsing -ErrorAction SilentlyContinue).StatusCode
  if ($exists -eq 200) {
    Write-Host "Realm '$REALM' já existe."
  } else {
    Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Post -Body (@{realm=$REALM; enabled=$true} | ConvertTo-Json)
    Write-Host "Realm '$REALM' criado."
  }
}

function Ensure-RoleExists {
  param($role, $token)
  $exists = $false
  try {
    $resp = Invoke-WebRequest -Uri "$KEYCLOAK_URL/admin/realms/$REALM/roles/$role" -Headers @{Authorization = "Bearer $token"} -Method Get -UseBasicParsing -ErrorAction Stop
    if ($resp.StatusCode -eq 200) {
      $exists = $true
    }
  } catch {
    # Se derro, assume que a role não existe
    $exists = $false
  }
  if ($exists) {
    Write-Host "Role '$role' já existe."
  } else {
    Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/roles" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Post -Body (@{name=$role; description="Role $role do domínio"} | ConvertTo-Json)
    Write-Host "Role '$role' criada."
  }
}

function Ensure-AllDomainRoles {
  param($token)
  foreach ($role in $ROLES) {
    Ensure-RoleExists $role $token
  }
}

function Ensure-ClientExists {
  param($token)
  # Buscar todos os clients e filtrar manualmente
  $clients = Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/clients" -Headers @{Authorization = "Bearer $token"} -Method Get
  $client = $clients | Where-Object { $_.clientId -eq $global:CLIENT_ID }
  $client_id = if ($client) { $client.id } else { $null }
  if (-not $client_id) {
    # Cria o client COM o campo 'name' igual ao 'clientId' e força string
    $clientBody = @{
      clientId = "$global:CLIENT_ID"
      name = "$global:CLIENT_ID"
      enabled = $true
      directAccessGrantsEnabled = $true
      serviceAccountsEnabled = $true
      publicClient = $false
      protocol = 'openid-connect'
    } | ConvertTo-Json
    Write-Host "DEBUG: Criando client com clientId = $global:CLIENT_ID"
    Write-Host "DEBUG: JSON enviado: $clientBody"
    Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/clients" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Post -Body $clientBody
    Write-Host "Client '$global:CLIENT_ID' criado."
    # Buscar o client_id recém-criado, com retry
    $retries = 0
    $client_id = $null
    while ($retries -lt 5 -and -not $client_id) {
      Start-Sleep -Seconds 2
      $clients = Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/clients" -Headers @{Authorization = "Bearer $token"} -Method Get
      $client = $clients | Where-Object { $_.clientId -eq $global:CLIENT_ID }
      if ($client) {
        $client_id = $client.id
      }
      $retries++
    }
    if (-not $client_id) {
      Write-Host "[ERRO] Não foi possível encontrar o client '$global:CLIENT_ID' após a criação. Resposta:"
      $clients | ConvertTo-Json | Write-Host
      exit 1
    }
  } else {
    Write-Host "Client '$global:CLIENT_ID' já existe."
  }
  # Atualiza secret e configs
  Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/clients/$client_id" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Put -Body (@{
    secret = $CLIENT_SECRET
    directAccessGrantsEnabled = $true
    serviceAccountsEnabled = $true
    publicClient = $false
    protocol = 'openid-connect'
  } | ConvertTo-Json)
}

function Ensure-UserExists {
  param($token)
  $users = Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/users?username=$TEST_USER" -Headers @{Authorization = "Bearer $token"} -Method Get
  $user_id = if ($users.Count -gt 0) { $users[0].id } else { $null }
  if (-not $user_id) {
    Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/users" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Post -Body (@{
      username = $TEST_USER
      enabled = $true
      emailVerified = $true
      email = "$<EMAIL>"
    } | ConvertTo-Json)
    Write-Host "Usuário '$TEST_USER' criado."
    $users = Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/users?username=$TEST_USER" -Headers @{Authorization = "Bearer $token"} -Method Get
    $user_id = $users[0].id
  } else {
    Write-Host "Usuário '$TEST_USER' já existe."
    # Atualiza email
    Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/users/$user_id" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Put -Body (@{
      email = "$<EMAIL>"
      emailVerified = $true
    } | ConvertTo-Json)
  }
  # Define senha
  Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM/users/$user_id/reset-password" -Headers @{Authorization = "Bearer $token"; 'Content-Type' = 'application/json'} -Method Put -Body (@{
    type = 'password'
    value = $TEST_PASS
    temporary = $false
  } | ConvertTo-Json)
}

function Validate-ClientAndUser {
  $params = "grant_type=password&client_id=$global:CLIENT_ID&client_secret=$CLIENT_SECRET&username=$TEST_USER&password=$TEST_PASS"
  $response = Invoke-WebRequest -Uri "$KEYCLOAK_URL/realms/$REALM/protocol/openid-connect/token" -Method Post -Body $params -ContentType 'application/x-www-form-urlencoded' -UseBasicParsing
  $content = $response.Content | ConvertFrom-Json
  if ($content.access_token) {
    Write-Host "Validação do client e usuário OK."
  } else {
    Write-Host "Erro ao validar client/usuário:"
    $response.Content | Write-Host
    exit 1
  }
}

# Execução principal
$token = Get-AdminToken
Ensure-RealmExists $token
Ensure-AllDomainRoles $token
Write-Host "DEBUG: CLIENT_ID = $global:CLIENT_ID"
if (-not $global:CLIENT_ID -or $global:CLIENT_ID -eq "null") {
  Write-Host "[ERRO] CLIENT_ID não está definido corretamente. Defina $global:CLIENT_ID no início do script."
  exit 1
}
Ensure-ClientExists $token
Ensure-UserExists $token
Validate-ClientAndUser

Write-Host "Setup do Keycloak finalizado com sucesso!" 