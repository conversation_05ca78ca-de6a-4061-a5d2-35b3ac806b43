import { PayablesType } from './payables-type.entity';

describe('PayablesType Entity', () => {
  const validPayablesTypeData = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    code: 'OFFICE',
    description: 'Despesas de Escritório',
    createdBy: '11111111-2222-3333-4444-555555555555',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    updatedBy: '11111111-2222-3333-4444-555555555555',
  };

  it('should create a valid payables type', () => {
    const payablesType = new PayablesType(
      validPayablesTypeData.id,
      validPayablesTypeData.code,
      validPayablesTypeData.description,
      validPayablesTypeData.createdBy,
      validPayablesTypeData.updatedBy,
      validPayablesTypeData.createdAt,
      validPayablesTypeData.updatedAt,
    );

    expect(payablesType.id).toBe(validPayablesTypeData.id);
    expect(payablesType.code).toBe(validPayablesTypeData.code);
    expect(payablesType.description).toBe(validPayablesTypeData.description);
    expect(payablesType.createdBy).toBe(validPayablesTypeData.createdBy);
    expect(payablesType.createdAt).toBe(validPayablesTypeData.createdAt);
    expect(payablesType.updatedBy).toBe(validPayablesTypeData.updatedBy);
    expect(payablesType.updatedAt).toBe(validPayablesTypeData.updatedAt);
  });

  it('should throw error when id is missing', () => {
    expect(() => {
      new PayablesType(
        '',
        validPayablesTypeData.code,
        validPayablesTypeData.description,
        validPayablesTypeData.createdBy,
      );
    }).toThrow('ID is required');
  });

  it('should throw error when code is missing', () => {
    expect(() => {
      new PayablesType(
        validPayablesTypeData.id,
        '',
        validPayablesTypeData.description,
        validPayablesTypeData.createdBy,
      );
    }).toThrow('Code is required');
  });

  it('should throw error when code is too long', () => {
    expect(() => {
      new PayablesType(
        validPayablesTypeData.id,
        'A'.repeat(51),
        validPayablesTypeData.description,
        validPayablesTypeData.createdBy,
      );
    }).toThrow('Code must be at most 50 characters');
  });

  it('should throw error when description is missing', () => {
    expect(() => {
      new PayablesType(
        validPayablesTypeData.id,
        validPayablesTypeData.code,
        '',
        validPayablesTypeData.createdBy,
      );
    }).toThrow('Description is required');
  });

  it('should throw error when description is too long', () => {
    expect(() => {
      new PayablesType(
        validPayablesTypeData.id,
        validPayablesTypeData.code,
        'A'.repeat(256),
        validPayablesTypeData.createdBy,
      );
    }).toThrow('Description must be at most 255 characters');
  });

  it('should throw error when createdBy is missing', () => {
    expect(() => {
      new PayablesType(
        validPayablesTypeData.id,
        validPayablesTypeData.code,
        validPayablesTypeData.description,
        '',
      );
    }).toThrow('CreatedBy is required');
  });

  it('should convert to JSON correctly', () => {
    const payablesType = new PayablesType(
      validPayablesTypeData.id,
      validPayablesTypeData.code,
      validPayablesTypeData.description,
      validPayablesTypeData.createdBy,
      validPayablesTypeData.updatedBy,
      validPayablesTypeData.createdAt,
      validPayablesTypeData.updatedAt,
    );

    const json = payablesType.toJSON();

    expect(json).toEqual({
      id: validPayablesTypeData.id,
      code: validPayablesTypeData.code,
      description: validPayablesTypeData.description,
      createdBy: validPayablesTypeData.createdBy,
      createdAt: validPayablesTypeData.createdAt.toISOString(),
      updatedBy: validPayablesTypeData.updatedBy,
      updatedAt: validPayablesTypeData.updatedAt.toISOString(),
    });
  });
});
