import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.test' });

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { KeycloakAdminUtils } from '../../keycloak.admin.utils';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../utils/request.utils.service';

jest.mock('axios');

describe('KeycloakAdminUtils', () => {
  let configService: ConfigService;
  let requestUtilsService: RequestUtilsService;
  let utils: KeycloakAdminUtils;

  beforeEach(() => {
    configService = {
      get: jest.fn((key: string) => {
        const config = {
          KEYCLOAK_BASE_URL: 'http://localhost:8080',
          KEYCLOAK_REALM: 'test-realm',
          KEYCLOAK_CLIENT_ID: 'test-client',
          KEYCLOAK_CLIENT_SECRET: 'test-secret',
        };
        return config[key] ?? process.env[key];
      }),
    } as unknown as ConfigService;
    requestUtilsService = {
      executeWithRetry: jest.fn((fn: () => unknown) => {
        return fn();
      }),
    } as unknown as RequestUtilsService;
    utils = new KeycloakAdminUtils(configService, requestUtilsService);
  });

  it('should throw if config is missing', () => {
    const emptyConfigService = {
      get: jest.fn().mockReturnValue(undefined),
    } as unknown as ConfigService;

    expect(
      () => new KeycloakAdminUtils(emptyConfigService, requestUtilsService),
    ).toThrow('Missing required Keycloak configuration');
  });

  it('should invalidate token', () => {
    (utils as unknown as { adminToken: string | null }).adminToken = 'token';
    (utils as unknown as { tokenExpiration: Date | null }).tokenExpiration =
      new Date();
    utils.invalidateToken();
    expect(
      (utils as unknown as { adminToken: string | null }).adminToken,
    ).toBeNull();
    expect(
      (utils as unknown as { tokenExpiration: Date | null }).tokenExpiration,
    ).toBeNull();
  });

  it('should handle getAdminToken error', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => {
        throw new Error('fail');
      },
    );
    await expect(utils.getAdminToken()).rejects.toThrow('fail');
  });

  it('should call getAdminAuthHeaders', async () => {
    (
      utils as unknown as { getAdminToken: () => Promise<string> }
    ).getAdminToken = jest.fn(() => Promise.resolve('token'));
    const headers = await utils.getAdminAuthHeaders();
    expect(headers.Authorization).toBe('Bearer token');
  });

  it('should handle listUsers error', async () => {
    (
      utils as unknown as {
        getAdminAuthHeaders: () => Promise<{ Authorization: string }>;
      }
    ).getAdminAuthHeaders = jest.fn(() => Promise.reject(new Error('fail')));
    await expect(utils.listUsers()).rejects.toThrow('fail');
  });

  it('should handle findUserByEmail error', async () => {
    jest
      .spyOn(utils, 'listUsers')
      .mockImplementation(() => Promise.reject(new Error('fail')));
    await expect(utils.findUserByEmail('mail')).resolves.toBeNull();
  });

  it('should handle createRoleIfNotExists error', async () => {
    jest
      .spyOn(utils, 'getAdminAuthHeaders')
      .mockImplementation(() => Promise.reject(new Error('fail')));
    await expect(utils.createRoleIfNotExists('role')).rejects.toThrow('fail');
  });

  it('should handle createUser error', async () => {
    (global as { fetch?: typeof fetch }).fetch = jest.fn(() =>
      Promise.resolve({ ok: false, headers: { get: () => null } }),
    ) as unknown as typeof fetch;
    await expect(utils.createUser('token', {})).rejects.toThrow();
  });

  it('should handle updateUser error', async () => {
    (global as { fetch?: typeof fetch }).fetch = jest.fn(() =>
      Promise.resolve({ ok: false, statusText: 'fail' }),
    ) as unknown as typeof fetch;
    await expect(utils.updateUser('token', 'id', {})).rejects.toThrow('fail');
  });

  it('should handle deleteUser error', async () => {
    (global as { fetch?: typeof fetch }).fetch = jest.fn(() =>
      Promise.resolve({ ok: false, statusText: 'fail' }),
    ) as unknown as typeof fetch;
    await expect(utils.deleteUser('token', 'id')).rejects.toThrow('fail');
  });

  it('should handle assignRole error', async () => {
    (global as { fetch?: typeof fetch }).fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        statusText: 'fail',
        json: () => ({}),
      }),
    ) as unknown as typeof fetch;
    await expect(utils.assignRole('token', 'id', 'role')).rejects.toThrow();
  });

  it('should handle removeRole error', async () => {
    (global as { fetch?: typeof fetch }).fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        statusText: 'fail',
        json: () => ({}),
      }),
    ) as unknown as typeof fetch;
    await expect(utils.removeRole('token', 'id', 'role')).rejects.toThrow(
      'fail',
    );
  });
});
