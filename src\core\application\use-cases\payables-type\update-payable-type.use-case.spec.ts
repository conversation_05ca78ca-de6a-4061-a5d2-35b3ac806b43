import { Test, TestingModule } from '@nestjs/testing';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '../../../ports/repositories/payables-type-repository.port';
import { PayablesType } from '../../../domain/payables-type/entities/payables-type.entity';
import { NotFoundException } from '@nestjs/common';
import { UpdatePayablesTypeUseCase } from './update-payable-type.use-case';

describe('UpdatePayableTypeUseCase', () => {
  let useCase: UpdatePayablesTypeUseCase;
  let repository: jest.Mocked<PayablesTypeRepositoryPort>;

  const mockPayableType = new PayablesType(
    'test-id',
    'test-code',
    'test-description',
    'test-created-by',
    'test-updated-by',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<PayablesTypeRepositoryPort> = {
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByCode: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdatePayablesTypeUseCase,
        {
          provide: PAYABLES_TYPE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdatePayablesTypeUseCase>(UpdatePayablesTypeUseCase);
    repository = module.get<jest.Mocked<PayablesTypeRepositoryPort>>(
      PAYABLES_TYPE_REPOSITORY,
    );
  });

  describe('execute', () => {
    it('should update supplier when user is authorized', async () => {
      // Arrange
      repository.findByUuid.mockResolvedValue(mockPayableType);
      repository.update.mockImplementation((payableType) =>
        Promise.resolve(payableType),
      );

      // Act
      const result = await useCase.execute('test-id', {
        code: 'New Code',
        description: 'New Description',
        updatedBy: 'test-updated-by',
      });

      // Assert
      expect(result.code).toBe('New Code');
      expect(result.description).toBe('New Description');
      expect(repository.update.mock.calls.length).toBeGreaterThan(0);
    });

    it('should throw NotFoundException when supplier does not exist', async () => {
      // Arrange
      repository.findByUuid.mockResolvedValue(null);

      // Act & Assert
      await expect(
        useCase.execute('non-existent-id', {
          code: 'New Code',
          description: 'New Description',
          updatedBy: 'test-updated-by',
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
