import {
  Inject,
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { UserRepository } from '../../core/ports/repositories/user-repository.interface';
import { User } from '../../core/domain/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserService as ApplicationUserService } from '../../core/application/services/user.service';
import { KeycloakIdentityProviderService } from '../../infrastructure/keycloak/keycloak-identity-provider.service';

@Injectable()
export class UsersService {
  private applicationService: ApplicationUserService;

  constructor(
    @Inject('UserRepository')
    private userRepository: UserRepository,
    private keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {
    this.applicationService = new ApplicationUserService(
      userRepository,
      keycloakIdentityProvider,
    );
  }

  async findAll(): Promise<User[]> {
    return this.applicationService.findAll();
  }

  async findById(id: string): Promise<User> {
    const user = await this.applicationService.findById(id);

    if (!user) {
      throw new NotFoundException(`Usuário com ID ${id} não encontrado`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    try {
      const user = await this.applicationService.findByEmail(email);

      if (!user) {
        throw new NotFoundException(
          `Usuário com email ${email} não encontrado`,
        );
      }

      return user;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Usuário com email ${email} não encontrado`);
    }
  }

  async findByKeycloakId(keycloakId: string): Promise<User> {
    const user = await this.applicationService.findByKeycloakId(keycloakId);

    if (!user) {
      throw new NotFoundException(
        `Usuário com Keycloak ID ${keycloakId} não encontrado`,
      );
    }

    return user;
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      return await this.applicationService.create(
        createUserDto.name,
        createUserDto.email,
        createUserDto.password,
        createUserDto.role,
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('já existe')) {
        throw new ConflictException(error.message);
      }
      throw error;
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    try {
      return await this.applicationService.update(
        id,
        updateUserDto.name,
        updateUserDto.password,
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('não encontrado')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.applicationService.delete(id);
    } catch (error) {
      if (error instanceof Error && error.message.includes('não encontrado')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }
}
