import { Test, TestingModule } from '@nestjs/testing';
import { SectorsController } from './sectors.controller';
import { SectorsService } from './sectors.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Sector } from '@/core/domain/entities/sector.entity';
import { ConfigService } from '@nestjs/config';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { ListSectorsQueryDto } from './dto/list-sectors-query.dto';

describe('SectorsController', () => {
  let controller: SectorsController;
  let mockSectorsService: {
    createSector: jest.Mock;
    getSectorByUuid: jest.Mock;
    updateSector: jest.Mock;
    deleteSector: jest.Mock;
    listSectors: jest.Mock;
  };

  const mockSector = Sector.create(
    1,
    'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
    'HR',
    'Human Resources',
    'creator-uuid',
    'updater-uuid',
    new Date('2025-05-07T00:00:00Z'),
    new Date('2025-05-07T00:00:00Z'),
  );

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    mockSectorsService = {
      createSector: jest.fn(),
      getSectorByUuid: jest.fn(),
      updateSector: jest.fn(),
      deleteSector: jest.fn(),
      listSectors: jest.fn().mockResolvedValue({
        items: [mockSector],
        total: 1,
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SectorsController],
      providers: [
        {
          provide: SectorsService,
          useValue: mockSectorsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<SectorsController>(SectorsController);
  });

  describe('getSectorByUuid', () => {
    it('should return a sector when found', async () => {
      mockSectorsService.getSectorByUuid.mockResolvedValue(mockSector);

      const result = await controller.getSectorByUuid({
        uuid: 'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
      });

      expect(result).toEqual({
        uuid: mockSector.uuid,
        code: mockSector.code,
        description: mockSector.description,
        createdBy: mockSector.createdBy,
        updatedBy: mockSector.updatedBy,
        createdAt: mockSector.createdAt.toISOString(),
        updatedAt: mockSector.updatedAt.toISOString(),
      });
      expect(mockSectorsService.getSectorByUuid).toHaveBeenCalledWith(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
      );
    });

    it('should throw NotFoundException when sector is not found', async () => {
      mockSectorsService.getSectorByUuid.mockRejectedValue(
        new NotFoundException('Sector not found'),
      );

      await expect(
        controller.getSectorByUuid({
          uuid: 'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
        }),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid UUID format', async () => {
      await expect(
        controller.getSectorByUuid({ uuid: 'invalid-uuid' }),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateSector', () => {
    it('should update a sector successfully', async () => {
      mockSectorsService.updateSector.mockResolvedValue(mockSector);

      const result = await controller.updateSectorPatch(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
        {
          code: 'HR',
          description: 'Human Resources',
          updatedBy: 'updater-uuid',
        },
      );

      expect(result).toEqual({
        uuid: mockSector.uuid,
        code: mockSector.code,
        description: mockSector.description,
        createdBy: mockSector.createdBy,
        updatedBy: mockSector.updatedBy,
        createdAt: mockSector.createdAt.toISOString(),
        updatedAt: mockSector.updatedAt.toISOString(),
      });

      expect(mockSectorsService.updateSector).toHaveBeenCalledWith(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
        {
          code: 'HR',
          description: 'Human Resources',
          updatedBy: 'updater-uuid',
        },
      );
    });

    it('should throw NotFoundException when sector is not found', async () => {
      mockSectorsService.updateSector.mockRejectedValue(
        new NotFoundException('Sector not found'),
      );

      await expect(
        controller.updateSectorPatch('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456', {
          code: 'HR',
          description: 'Human Resources',
          updatedBy: 'updater-uuid',
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteSector', () => {
    it('should delete a sector successfully', async () => {
      mockSectorsService.deleteSector.mockResolvedValue(undefined);

      await controller.delete('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456');

      expect(mockSectorsService.deleteSector).toHaveBeenCalledWith(
        'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
      );
    });

    it('should throw NotFoundException when sector is not found', async () => {
      mockSectorsService.deleteSector.mockRejectedValue(
        new NotFoundException('Sector not found'),
      );

      await expect(
        controller.delete('b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('listSectors', () => {
    it('should return a list of sectors with pagination', async () => {
      const query: ListSectorsQueryDto = {
        limit: 5,
        offset: 0,
      };

      const result = await controller.listSectors(query);

      expect(result.items).toHaveLength(1);
      expect(result.limit).toBe(5);
      expect(result.offset).toBe(0);
      expect(result.total).toBe(1);
      expect(mockSectorsService.listSectors).toHaveBeenCalledWith(query);
    });

    it('should return a list of sectors with filters', async () => {
      const query: ListSectorsQueryDto = {
        limit: 5,
        offset: 0,
        code: 'HR',
        description: 'Human',
      };

      const result = await controller.listSectors(query);

      expect(result.items).toHaveLength(1);
      expect(result.limit).toBe(5);
      expect(result.offset).toBe(0);
      expect(result.total).toBe(1);
      expect(mockSectorsService.listSectors).toHaveBeenCalledWith(query);
    });
  });
});
