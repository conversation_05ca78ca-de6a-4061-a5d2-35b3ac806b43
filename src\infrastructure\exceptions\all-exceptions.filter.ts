/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { Request, Response } from 'express';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const exceptionResponse =
      exception instanceof HttpException
        ? exception.getResponse()
        : { message: 'Erro interno do servidor' };

    // Construir o corpo da resposta com tipagem segura
    const responseBody: Record<string, unknown> = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // Adicionar dados da exceção com verificação de tipo
    if (typeof exceptionResponse === 'string') {
      responseBody.message = exceptionResponse;
    } else if (exceptionResponse && typeof exceptionResponse === 'object') {
      // Copiar propriedades do objeto de resposta de forma segura
      Object.entries(exceptionResponse as Record<string, unknown>).forEach(
        ([key, value]) => {
          responseBody[key] = value;
        },
      );
    } else {
      // Fallback para caso de tipo inesperado
      responseBody.message = 'Erro desconhecido';
    }

    // Log detalhado do erro com tipagem segura
    const errorLog = {
      message: 'Exceção capturada pelo filtro global',
      exception: this.safeStringify(exception),
      request: {
        url: request.url,
        method: request.method,
        headers: this.sanitizeHeaders(request.headers),
        body: request.body,
        params: request.params,
        query: request.query,
      },
      response: responseBody,
    };

    this.logger.error(errorLog);

    httpAdapter.reply(response, responseBody, httpStatus);
  }

  private safeStringify(value: unknown): string {
    if (value instanceof Error) {
      return value.stack || value.message;
    }
    if (value === null || value === undefined) {
      return 'Erro desconhecido';
    }
    try {
      return JSON.stringify(value);
    } catch {
      return '[object Object]';
    }
  }

  // Método para sanitizar headers e evitar problemas de tipagem
  private sanitizeHeaders(headers: unknown): Record<string, string> {
    if (headers && typeof headers === 'object') {
      const safeHeaders: Record<string, string> = {};

      for (const [key, value] of Object.entries(
        headers as Record<string, unknown>,
      )) {
        if (typeof value === 'string') {
          safeHeaders[key] = value;
        } else if (value !== undefined && value !== null) {
          try {
            safeHeaders[key] = JSON.stringify(value);
          } catch {
            safeHeaders[key] = '[Valor não serializável]';
          }
        }
      }

      return safeHeaders;
    }
    return {};
  }
}
