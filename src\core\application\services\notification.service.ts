import { Injectable } from '@nestjs/common';
import { RabbitMQService } from '../../../infrastructure/messaging/rabbitmq/rabbitmq.service';

interface NotificationPayload {
  userId: string;
  title: string;
  message: string;
  type: 'email' | 'push' | 'sms';
  metadata?: Record<string, unknown>;
}

@Injectable()
export class NotificationService {
  private readonly EXCHANGE = 'notifications';

  constructor(private readonly rabbitMQService: RabbitMQService) {
    // Inicializar as filas e bindings ao iniciar o serviço
    void this.setupQueuesAndBindings();
  }

  private async setupQueuesAndBindings() {
    // Configurar filas para diferentes tipos de notificações
    await this.rabbitMQService.bindQueue(
      'notifications.email',
      this.EXCHANGE,
      'notification.email',
    );
    await this.rabbitMQService.bindQueue(
      'notifications.push',
      this.EXCHANGE,
      'notification.push',
    );
    await this.rabbitMQService.bindQueue(
      'notifications.sms',
      this.EXCHANGE,
      'notification.sms',
    );
  }

  /**
   * Envia uma notificação através do RabbitMQ
   * @param payload Dados da notificação
   */
  async sendNotification(payload: NotificationPayload): Promise<void> {
    const routingKey = `notification.${payload.type}`;

    await this.rabbitMQService.publish(this.EXCHANGE, routingKey, {
      ...payload,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Exemplo de uso para enviar uma notificação por email
   * @param userId ID do usuário
   * @param title Título do email
   * @param message Mensagem do email
   */
  async sendEmailNotification(
    userId: string,
    title: string,
    message: string,
  ): Promise<void> {
    await this.sendNotification({
      userId,
      title,
      message,
      type: 'email',
      metadata: {
        priority: 'high',
        template: 'default',
      },
    });
  }

  /**
   * Exemplo de uso para enviar uma notificação push
   * @param userId ID do usuário
   * @param title Título da notificação
   * @param message Mensagem da notificação
   */
  async sendPushNotification(
    userId: string,
    title: string,
    message: string,
  ): Promise<void> {
    await this.sendNotification({
      userId,
      title,
      message,
      type: 'push',
      metadata: {
        requireInteraction: true,
        icon: 'notification-icon.png',
      },
    });
  }

  /**
   * Configura um consumidor para processar notificações de email
   * @param handler Função para processar a notificação
   */
  async setupEmailNotificationConsumer(
    handler: (notification: NotificationPayload) => Promise<void>,
  ): Promise<void> {
    // Adaptador para converter o formato da mensagem
    const messageAdapter = (message: Record<string, unknown>) => {
      // Verifica e converte os campos necessários
      if (
        typeof message.userId === 'string' &&
        typeof message.title === 'string' &&
        typeof message.message === 'string' &&
        typeof message.type === 'string'
      ) {
        const notificationPayload: NotificationPayload = {
          userId: message.userId,
          title: message.title,
          message: message.message,
          type: message.type as 'email' | 'push' | 'sms',
          metadata: message.metadata as Record<string, unknown>,
        };
        return handler(notificationPayload);
      }
      throw new Error('Formato de notificação inválido');
    };

    await this.rabbitMQService.consume('notifications.email', messageAdapter);
  }
}
