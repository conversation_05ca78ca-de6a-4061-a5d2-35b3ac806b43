import { ApiProperty } from '@nestjs/swagger';
import { PayablesTypeResponseDto } from './payables-type-response.dto';

export class PaginatedPayablesTypeResponseDto {
  @ApiProperty({ type: [PayablesTypeResponseDto] })
  items: PayablesTypeResponseDto[];

  @ApiProperty({ description: 'Total number of items' })
  total: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Number of items skipped' })
  offset: number;
}
