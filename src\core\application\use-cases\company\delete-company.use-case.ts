import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { CompanyRepository } from '../../../ports/repositories/company-repository.interface';
import { COMPANY_REPOSITORY } from '../../../constants/injection-tokens';

export interface DeleteCompanyInput {
  uuid: string;
}

@Injectable()
export class DeleteCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(input: DeleteCompanyInput): Promise<void> {
    const existingCompany = await this.companyRepository.findByUuid(input.uuid);

    if (!existingCompany) {
      throw new NotFoundException(`Empresa não encontrada.`);
    }

    await this.companyRepository.delete(existingCompany.id);
  }
}
