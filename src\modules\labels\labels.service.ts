import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../infrastructure/prisma/prisma.service';
import { CreateLabelDto } from './dto/create-label.dto';
import { UpdateLabelDto } from './dto/update-label.dto';

@Injectable()
export class LabelsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: CreateLabelDto) {
    const exists = await this.prisma.label.findFirst({
      where: {
        modulo: dto.modulo,
        label: dto.label,
      },
    });
    if (exists) {
      throw new ConflictException({
        code: 'CONFLICT',
        message: 'Label já cadastrada com esses dados.'
      });
    }
    return this.prisma.label.create({ data: dto });
  }

  async findAll() {
    return this.prisma.label.findMany();
  }

  async findByModulo(modulo: string) {
    return this.prisma.label.findMany({ where: { modulo } });
  }

  async findOne(id: number) {
    const label = await this.prisma.label.findUnique({ where: { id } });
    if (!label) throw new NotFoundException('Label not found');
    return label;
  }

  async update(id: number, dto: UpdateLabelDto) {
    return this.prisma.label.update({ where: { id }, data: dto });
  }

  async delete(id: number) {
    return this.prisma.label.delete({ where: { id } });
  }
} 