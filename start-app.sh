#!/bin/bash

export NODE_ENV=development
export APP_PORT=3000
export APP_NAME="Backoffice Backend Service"
export APP_VERSION="1.0.0"

# Database
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/nest_boilerplate?schema=public"

# JWT
export JWT_SECRET="seu_jwt_secret_aqui_muito_seguro_123456789"
export JWT_EXPIRATION="3600"

# Keycloak
export KEYCLOAK_BASE_URL="http://localhost:8080"
export KEYCLOAK_REALM="master"
export KEYCLOAK_CLIENT_ID="backend-dev-client"
export KEYCLOAK_CLIENT_SECRET="myclientsecret"
export KEYCLOAK_ADMIN_USERNAME="admin"
export KEYCLOAK_ADMIN_PASSWORD="admin"

# RabbitMQ
export RABBITMQ_HOST="localhost"
export RABBITMQ_PORT="5672"
export RABBITMQ_USERNAME="guest"
export RABBITMQ_PASSWORD="guest"

# Email Configuration (opcional)
export SMTP_HOST="smtp.example.com"
export SMTP_PORT="587"
export SMTP_SECURE="false"
export SMTP_USER="<EMAIL>"
export SMTP_PASS="your-password"
export SMTP_FROM="<EMAIL>"
export FRONTEND_URL="https://app.company.com"

# OpenTelemetry
export OTEL_EXPORTER_OTLP_ENDPOINT="http://localhost:4318"

echo "Starting Backoffice Backend Service..."
echo "Swagger documentation will be available at: http://localhost:3000/v1/backoffice/api/docs"

npx ts-node src/main.ts 