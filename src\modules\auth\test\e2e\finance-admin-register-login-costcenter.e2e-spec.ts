import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../../app.module';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import * as dotenv from 'dotenv';

dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

jest.setTimeout(60000);

// Definições de tipos para as respostas
interface RegisterResponse {
  user: { id: string; email: string; role: string };
  access_token: string;
}

describe('E2E - Register FINANCE_ADMIN, Login e CostCenter', () => {
  let app: INestApplication;

  const testUser = {
    name: 'Finance Admin E2E',
    email: `financeadmin.e2e.${Date.now()}@test.com`,
    password: 'Senha123!',
    role: 'FINANCE_ADMIN',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true }), AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('deve registrar, logar e acessar todos os fluxos de FINANCE_ADMIN', async () => {
    // 1. Register
    const registerRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .post('/auth/register')
      .send(testUser);
    expect(registerRes.status).toBe(201);
    const registerBody = registerRes.body as RegisterResponse;
    expect(registerBody).toHaveProperty('user');
    expect(registerBody.user).toHaveProperty('role', 'FINANCE_ADMIN');
    expect(registerBody).toHaveProperty('access_token');

    // 2. Login
    const loginRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .post('/auth/login')
      .send({ username: testUser.email, password: testUser.password });
    expect(loginRes.status).toBe(201);
    const loginBody = loginRes.body as { access_token: string };
    expect(loginBody).toHaveProperty('access_token');
    const token = loginBody.access_token;

    // 3. /auth/me
    const meRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .get('/auth/me')
      .set('Authorization', `Bearer ${token}`);
    expect(meRes.status).toBe(200);
    const meBody = meRes.body as { id: string; roles: string[] };
    expect(meBody).toHaveProperty('roles');
    expect(meBody.roles).toContain('FINANCE_ADMIN');
    const userId = meBody.id;

    // 4. Cost Center CRUD
    // Create
    const ccCreateRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .post('/finance/cost-centers')
      .set('Authorization', `Bearer ${token}`)
      .send({
        description: 'Cost Center E2E',
        createdBy: userId,
        updatedBy: userId,
      });
    expect(ccCreateRes.status).toBe(201);
    const costCenter = ccCreateRes.body as { uuid: string };

    // List
    const ccListRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .get('/finance/cost-centers')
      .set('Authorization', `Bearer ${token}`);
    expect(ccListRes.status).toBe(200);
    const ccList = ccListRes.body as { items: unknown[] };
    console.log('ccListRes.body:', ccListRes.body);
    expect(Array.isArray(ccList.items)).toBe(true);

    // Update
    const ccUpdateRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .patch(`/finance/cost-centers/${costCenter.uuid}`)
      .set('Authorization', `Bearer ${token}`)
      .send({ description: 'Cost Center Atualizado E2E', updatedBy: userId });
    expect(ccUpdateRes.status).toBe(200);
    expect(ccUpdateRes.body).toHaveProperty(
      'description',
      'Cost Center Atualizado E2E',
    );

    // Delete
    const ccDeleteRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .delete(`/finance/cost-centers/${costCenter.uuid}`)
      .set('Authorization', `Bearer ${token}`);
    expect(ccDeleteRes.status).toBe(204);

    // 5. Payment Method Create/List
    const pmLabel = `PM-E2E-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    const pmCreateRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .post('/finance/payment-methods')
      .set('Authorization', `Bearer ${token}`)
      .send({ label: pmLabel, description: 'Payment Method E2E' });
    // Pode retornar 201 ou 500 se já existir, então só checa 201
    expect([201, 500]).toContain(pmCreateRes.status);

    const pmListRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .get('/finance/payment-methods')
      .set('Authorization', `Bearer ${token}`);
    expect(pmListRes.status).toBe(200);
    const pmList = pmListRes.body as { items: unknown[] };
    expect(Array.isArray(pmList.items)).toBe(true);

    // 6. Sectors List
    const sectorsRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .get('/finance/sectors')
      .set('Authorization', `Bearer ${token}`);
    expect(sectorsRes.status).toBe(200);
    const sectorsList = sectorsRes.body as { items: unknown[] };
    expect(Array.isArray(sectorsList.items)).toBe(true);

    // 7. Employees List
    const employeesRes = await request(
      app.getHttpServer() as unknown as import('http').Server,
    )
      .get('/core/employees')
      .set('Authorization', `Bearer ${token}`);
    expect(employeesRes.status).toBe(200);
    const employeesList = employeesRes.body as { items: unknown[] };
    expect(Array.isArray(employeesList.items)).toBe(true);
  });
});
