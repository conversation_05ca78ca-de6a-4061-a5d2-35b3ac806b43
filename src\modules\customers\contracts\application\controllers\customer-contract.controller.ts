import { Controller, Post, Get, Patch, Delete, Param, Body, HttpCode, HttpStatus, UploadedFile, UseInterceptors, UploadedFiles, Request, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { ContractDto, CreateContractDto, CustomerContractApiBodyDto, CustomerContractTextFormDataDto, CreateCustomerContractsDto, CustomerContractSignPatchDto } from '../../infrastructure/dtos/customercontract.dto';
import { CustomerContractResponseDto } from '../../infrastructure/dtos/customer-contract-response.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { CreateCustomerContractUseCase } from '../use-cases/create-customer-contract.use-case';
import { ListCustomerContractsUseCase } from '../use-cases/list-customer-contracts.use-case';
import { GetCustomerContractUseCase } from '../use-cases/get-customer-contract.use-case';
import { UpdateCustomerContractUseCase } from '../use-cases/update-customer-contract.use-case';
import { DeleteCustomerContractUseCase } from '../use-cases/delete-customer-contract.use-case';
import { JwtAuthGuard } from '../../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../../auth/guards/roles.guard';
import { Roles } from '../../../../auth/decorators/roles.decorator';
import { Role } from '../../../../../core/domain/role.enum';


interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    [key: string]: any;
  };
}

@ApiTags('Customer Contracts')
@ApiBearerAuth()
@Controller('core/customers/:uuid/contracts')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CustomerContractController {
  constructor(
    private readonly createCustomerContractUseCase: CreateCustomerContractUseCase,
    private readonly listCustomerContractsUseCase: ListCustomerContractsUseCase,
    private readonly getCustomerContractUseCase: GetCustomerContractUseCase,
    private readonly updateCustomerContractUseCase: UpdateCustomerContractUseCase,
    private readonly deleteCustomerContractUseCase: DeleteCustomerContractUseCase,
  ) {}

  @Post()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiOperation({
    summary: 'Criar contratos para o cliente e fazer upload dos arquivos.',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Cliente',
    type: String,
  })
  @ApiBody({
    description:
      "Dados para criação de contratos. 'files' são os arquivos e 'contractsMetadata' é uma string JSON com os metadados.",
    type: CustomerContractApiBodyDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Contratos criados com sucesso.',
    type: Array,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiResponse({ status: 403, description: 'Acesso negado.' })
  async createContracts(
    @Param('uuid') customerUuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() formData: CustomerContractTextFormDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<ContractDto[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    let contractsMetadataArray: unknown;
    try {
      contractsMetadataArray = JSON.parse(formData.contractsMetadata);
    } catch (_error) {
      throw new Error(
        'O campo contractsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(contractsMetadataArray) ||
      files.length !== contractsMetadataArray.length
    ) {
      throw new Error(
        'O número de metadatos de contrato não corresponde ao número de arquivos enviados.',
      );
    }

    const processedContractsData = (contractsMetadataArray as unknown[]).map((meta) => ({
      ...(meta as Record<string, unknown>),
      customerUuid: customerUuid,
      uploadedBy: req.user.id,
    }));

    const createContractsDto: CreateCustomerContractsDto = {
      contracts: processedContractsData as never,
    };

    const contracts = await this.createCustomerContractUseCase.executeMultiple(
      customerUuid,
      files,
      req.user.id,
      createContractsDto,
    );

    return contracts.map(contract => ({
      uuid: contract.uuid,
      customerUuid: contract.customerUuid,
      name: contract.name,
      url: undefined,
      isSigned: contract.isSigned,
      downloadUrl: contract.downloadUrl,
      fileName: contract.fileName,
      status: contract.status,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    }));
  }

  @Post('legacy')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Criar contrato para o customer (método legado)' })
  @ApiResponse({ status: 201, type: ContractDto })
  @ApiBody({ type: CreateContractDto })
  async createContract(
    @Param('uuid') customerUuid: string,
    @Body() dto: any, // não validar multipart
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ContractDto> {
    
    const contract = await this.createCustomerContractUseCase.execute(customerUuid, {
      isSigned: dto.isSigned === 'true' || dto.isSigned === true,
      file
    });
    return {
      uuid: contract.uuid,
      customerUuid: contract.customerUuid,
      name: contract.name,
      url: undefined,
      isSigned: contract.isSigned,
      downloadUrl: null,
      fileName: null,
      status: contract.status,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    };
  }

  @Get()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'Listar contratos do customer' })
  @ApiResponse({ status: 200, type: [CustomerContractResponseDto] })
  async getContracts(
    @Param('uuid') customerUuid: string,
  ): Promise<CustomerContractResponseDto[]> {
    return await this.listCustomerContractsUseCase.execute(customerUuid);
  }

  @Get(':contractUuid')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'Buscar contrato específico do customer' })
  @ApiResponse({ status: 200, type: ContractDto })
  async getContract(
    @Param('uuid') customerUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<ContractDto> {
    const contract = await this.getCustomerContractUseCase.execute(customerUuid, contractUuid);
    if (!contract) throw new Error('Contract not found');
    return {
      uuid: contract.uuid,
      customerUuid: contract.customerUuid,
      name: contract.name,
      url: undefined,
      isSigned: contract.isSigned,
      downloadUrl: null,
      fileName: null,
      status: contract.status,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    };
  }

  @Patch(':contractUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Assinar ou rejeitar contrato do cliente' })
  @ApiParam({ name: 'uuid', required: true, description: 'UUID do Cliente', type: String })
  @ApiParam({ name: 'contractUuid', required: true, description: 'UUID do Contrato', type: String })
  @ApiBody({ description: 'Campos para assinatura/rejeição do contrato', type: CustomerContractSignPatchDto })
  @ApiResponse({ status: 200, description: 'Contrato atualizado com sucesso.', type: ContractDto })
  @ApiResponse({ status: 404, description: 'Contrato não encontrado para este cliente.' })
  async patchContract(
    @Param('uuid') customerUuid: string,
    @Param('contractUuid') contractUuid: string,
    @Body() patchDto: CustomerContractSignPatchDto,
  ): Promise<ContractDto> {
    const updated = await this.updateCustomerContractUseCase.execute(customerUuid, contractUuid, {
      isSigned: patchDto.isSigned,
    } as any);
    return {
      uuid: updated.uuid,
      customerUuid: updated.customerUuid,
      name: updated.name,
      url: undefined,
      isSigned: updated.isSigned,
      downloadUrl: null,
      fileName: null,
      status: updated.status,
      createdAt: updated.createdAt,
      updatedAt: updated.updatedAt,
    };
  }

  @Delete(':contractUuid')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover contrato do customer' })
  @ApiResponse({ status: 204 })
  async deleteContract(
    @Param('uuid') customerUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<void> {
    await this.deleteCustomerContractUseCase.execute(customerUuid, contractUuid);
  }
} 