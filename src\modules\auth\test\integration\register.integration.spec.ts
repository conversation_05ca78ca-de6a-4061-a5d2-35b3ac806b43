import { Test, TestingModule } from '@nestjs/testing';
import { Auth<PERSON>ontroller } from '../../auth.controller';
import { AuthService } from '../../auth.service';
import { JwtService } from '@nestjs/jwt';
import { KeycloakService } from '../../../../infrastructure/keycloak/keycloak.service';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { EventPublisherService } from '../../../../infrastructure/events/event-publisher.service';
import { RegisterDto } from '../../dto/register.dto';
import { Role } from '../../../../core/domain/role.enum';
import { EventsTestModule } from '../../../../infrastructure/events/test/events-test.module';
import { UsersService } from '../../../users/users.service';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../../infrastructure/utils/request.utils.service';
import {
  AUTH_TEST_CONFIG,
  mockConfigService,
  mockRequestUtilsService,
  extendKeycloakServiceForTesting,
} from './auth-test.config';
import axios from 'axios';
// import { EmailService } from '../../../../infrastructure/email/nodemailer-email.service.interface';

// Mock axios for tests
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Exportando constante para uso em outros testes
export const AuthTestUser = {
  email: `test-user-${uuidv4().substring(0, 8)}@example.com`,
  password: 'Test@123456',
  name: 'Test User',
  accessToken: null as string | null,
  refreshToken: null as string | null,
  keycloakId: AUTH_TEST_CONFIG.mock.keycloakUserId, // Add a mock Keycloak ID to prevent dependency issues between tests
};

describe('AuthController - Register', () => {
  let controller: AuthController;
  let _authService: AuthService;
  let _keycloakIdentityProvider: KeycloakIdentityProviderService;
  let _keycloakService: KeycloakService;

  const mockUsersService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockUserRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    updateUserKeycloakId: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    decode: jest.fn(),
  };

  const mockKeycloakService = {
    token: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    validateToken: jest.fn(),
    getUserInfo: jest.fn(),
  };

  const mockKeycloakIdentityProvider = {
    registerUser: jest.fn(),
    assignUserRoles: jest.fn((userId: string, roles: string[]) => {
      // Normaliza para maiúsculo para garantir compatibilidade
      const normalizedRoles = (roles || []).map((r) => String(r).toUpperCase());
      if (JSON.stringify(normalizedRoles) !== JSON.stringify(['USER'])) {
        throw new Error(
          `assignUserRoles chamado com roles incorretas: ${JSON.stringify(roles)}`,
        );
      }
      return Promise.resolve();
    }),
    authenticate: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    getUserInfo: jest.fn(),
    keycloakAdminUtils: {
      getAdminAuthHeaders: jest.fn().mockResolvedValue({
        Authorization: 'Bearer mock-token',
        'Content-Type': 'application/json',
      }),
    },
  };

  const mockEventPublisher = {
    publish: jest.fn(),
  };

  const mockPasswordResetTokenRepository = {
    create: jest.fn(),
    findByToken: jest.fn(),
    markAsUsed: jest.fn(),
  };

  const mockEmailService = {
    sendResetPasswordEmail: jest.fn(),
  };

  beforeEach(async () => {
    // Reset axios mocks
    mockedAxios.post.mockReset();

    // Set up the axios post mock for user creation
    mockedAxios.post.mockResolvedValueOnce({
      status: 201,
      headers: {
        location: `${AUTH_TEST_CONFIG.keycloak.baseUrl}/admin/realms/${AUTH_TEST_CONFIG.keycloak.realm}/users/${AUTH_TEST_CONFIG.mock.keycloakUserId}`,
      },
    });

    const module: TestingModule = await Test.createTestingModule({
      imports: [EventsTestModule],
      controllers: [AuthController],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: 'PasswordResetTokenRepository',
          useValue: mockPasswordResetTokenRepository,
        },
        {
          provide: 'EmailService',
          useValue: mockEmailService,
        },
        {
          provide: KeycloakService,
          useValue: mockKeycloakService,
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakIdentityProvider,
        },
        {
          provide: EventPublisherService,
          useValue: mockEventPublisher,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RequestUtilsService,
          useValue: mockRequestUtilsService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    _authService = module.get<AuthService>(AuthService);
    _keycloakIdentityProvider = module.get<KeycloakIdentityProviderService>(
      KeycloakIdentityProviderService,
    );
    _keycloakService = module.get<KeycloakService>(KeycloakService);

    // Extend the KeycloakService with testing methods
    extendKeycloakServiceForTesting(_keycloakService);

    // Mock default implementations
    mockJwtService.sign.mockImplementation(() => 'mock-jwt-token');
    mockUserRepository.findByEmail.mockResolvedValue(null); // User not found by default
    mockUserRepository.updateUserKeycloakId.mockImplementation(
      (userId: string, keycloakId: string) =>
        Promise.resolve({ id: userId, keycloakId }),
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    const registerDto: RegisterDto = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    };

    const mockKeycloakUserId = AUTH_TEST_CONFIG.mock.keycloakUserId;
    const mockLocalUserId = 'local-uuid-456';

    it('should register a user in both Keycloak and local database', async () => {
      // Mock the Keycloak service responses
      mockKeycloakIdentityProvider.registerUser.mockResolvedValue(
        mockKeycloakUserId,
      );
      mockKeycloakIdentityProvider.assignUserRoles.mockResolvedValue(undefined);

      // Mock local registration
      mockUserRepository.create.mockResolvedValue({
        id: mockLocalUserId,
        email: registerDto.email,
        name: registerDto.name,
        role: Role.USER,
        password: 'hashed-password',
        createdAt: new Date(),
        updatedAt: new Date(),
        keycloakId: null,
      });

      // Execute register
      const result = await controller.register(registerDto);

      // Verify Keycloak integration
      expect(mockKeycloakIdentityProvider.registerUser).toHaveBeenCalledWith({
        username: registerDto.email,
        email: registerDto.email,
        firstName: 'Test',
        lastName: 'User',
        password: registerDto.password,
      });

      expect(mockKeycloakIdentityProvider.assignUserRoles).toHaveBeenCalledWith(
        mockKeycloakUserId,
        ['USER'],
      );

      // Verify local repository updates
      expect(mockUserRepository.updateUserKeycloakId).toHaveBeenCalledWith(
        mockLocalUserId,
        mockKeycloakUserId,
      );

      // Verify JWT token
      expect(mockJwtService.sign).toHaveBeenCalled();

      // Verify result structure
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('access_token');
      expect(result.user).toHaveProperty('keycloakId', mockKeycloakUserId);
    });

    it('should handle Keycloak registration failure', async () => {
      // Mock Keycloak registration failure
      const errorMessage = 'Keycloak registration failed';
      mockKeycloakIdentityProvider.registerUser.mockRejectedValue(
        new Error(errorMessage),
      );

      // Simulate axios error for the HTTP request
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));

      // Force AuthService to throw an error that can be caught by the controller
      mockUserRepository.create.mockRejectedValue(
        new Error('Should not be called'),
      );

      // Expect the register method to throw an error
      await expect(controller.register(registerDto)).rejects.toThrow();

      // Verify Keycloak was called
      expect(mockKeycloakIdentityProvider.registerUser).toHaveBeenCalled();

      // We don't need to check if create was called because in a real scenario
      // the error from Keycloak would prevent further execution
    });

    it('should handle local registration failure', async () => {
      // Mock Keycloak success but local failure
      mockKeycloakIdentityProvider.registerUser.mockResolvedValue(
        mockKeycloakUserId,
      );
      mockKeycloakIdentityProvider.assignUserRoles.mockResolvedValue(undefined);

      // Mock local registration to fail
      const errorMessage = 'Email already exists';
      mockUserRepository.create.mockRejectedValue(new Error(errorMessage));

      // Expect register to throw an error
      await expect(controller.register(registerDto)).rejects.toThrow();

      // Verify Keycloak was called
      expect(mockKeycloakIdentityProvider.registerUser).toHaveBeenCalled();
    });
  });
});
