import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../../app.module';
import { v4 as uuidv4 } from 'uuid';
import { fail } from 'assert';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
const fs = require('fs');

dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

jest.setTimeout(120000);

// Utilitário para criar supplier e obter token
async function createSupplierAndToken(app: INestApplication) {
  const testEmail = `e2e-contracts-${uuidv4().substring(0, 8)}@example.com`;
  const supplierEmail = `e2e-supplier-${uuidv4().substring(0, 8)}@example.com`;
  const testPassword = 'Test@123456';

  // Registrar usuário
  const registerResponse = await request(app.getHttpServer())
    .post('/auth/register')
    .send({
      email: testEmail,
      password: testPassword,
      name: 'E2E Contracts User',
      type: 'EMPLOYEE',
      cpf: '12345678901',
    });
  if (registerResponse.status !== 201) {
    fail(`Registration failed: ${registerResponse.status}`);
  }
  const registerAuthResponse = registerResponse.body;
  let keycloakUserId =
    registerAuthResponse.user?.keycloakId ||
    registerAuthResponse.user?.sub ||
    '';

  // Fallback: login e getUserInfo
  let accessToken: string | undefined;
  if (!keycloakUserId) {
    const tempLoginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ username: testEmail, password: testPassword });
    if (tempLoginResponse.status === 200) {
      accessToken = tempLoginResponse.body.access_token;
      if (accessToken) {
        const keycloakIdentityProviderService = app.get(KeycloakIdentityProviderService);
        try {
          const userInfo = await keycloakIdentityProviderService.getUserInfo(accessToken);
          keycloakUserId = userInfo.id;
        } catch (error) {
          // Continua sem keycloakUserId
        }
      }
    }
  }

  // Atribuir papel ADMIN
  if (keycloakUserId) {
    const keycloakIdentityProviderService = app.get(KeycloakIdentityProviderService);
    try {
      await keycloakIdentityProviderService.assignUserRoles(keycloakUserId, ['ADMIN']);
      await new Promise((resolve) => setTimeout(resolve, 5000));
    } catch (error) {
      // Continua mesmo se falhar
    }
  }

  // Login final para garantir token com role
  const loginResponse = await request(app.getHttpServer())
    .post('/auth/login')
    .send({ username: testEmail, password: testPassword });
  if (loginResponse.status !== 200) {
    fail(`Login failed: ${loginResponse.status}`);
  }
  accessToken = loginResponse.body.access_token;

  // Criar supplier
  const createDto = {
    name: 'E2E Supplier Contracts',
    document: `${Math.floor(Math.random() * 1e14)}`.padStart(14, '1'),
    tradeName: 'E2E Trade Name',
    address: {
      street: 'Rua Teste',
      city: 'Cidade Teste',
      zipCode: '12345-678',
      state: 'TS',
    },
    type: 'BANK',
    email: supplierEmail,
  };
  const supplierResponse = await request(app.getHttpServer())
    .post('/core/suppliers')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(createDto);
  if (supplierResponse.status !== 201) {
    fail(`Supplier creation failed: ${supplierResponse.status}`);
  }
  const createdSupplierId = supplierResponse.body.id || supplierResponse.body.uuid;
  return { accessToken, createdSupplierId };
}

describe('Supplier Contracts (e2e)', () => {
  jest.setTimeout(120000);
  let app: INestApplication;

  beforeAll(async () => {
    const mockStorageProvider: IStorageProvider = {
      upload: jest.fn().mockResolvedValue(undefined),
      getDownloadUrl: jest.fn().mockResolvedValue('https://fake-url.com/contract-e2e.pdf'),
      getFileUrl: jest.fn().mockResolvedValue('https://fake-url.com/contract-e2e.pdf'),
      getFileStream: jest.fn().mockResolvedValue({}),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          envFilePath: path.resolve(process.cwd(), '.env.test'),
          isGlobal: true,
        }),
        AppModule,
      ],
    })
      .overrideProvider('IStorageProvider')
      .useValue(mockStorageProvider)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        skipMissingProperties: false,
      }),
    );
    await app.init();
  });

  afterAll(async () => {
    if (app) await app.close();
  });

  it('should create and list supplier contracts', async () => {
    const { accessToken, createdSupplierId } = await createSupplierAndToken(app);
    const contractIdentifier = 'contract-e2e.pdf';
    const tempFilePath = path.join(__dirname, contractIdentifier);
    fs.writeFileSync(tempFilePath, 'fake contract content');
    const contractsMetadata = [
      {
        contractIdentifier,
        entityType: 'SUPPLIER',
        entityActualUuid: createdSupplierId,
        contractType: 'CERT_PLATFORM',
        signed: false,
        expirationDate: '2025-12-31',
      },
    ];
    let createResponse;
    try {
      createResponse = await request(app.getHttpServer())
        .post(`/core/suppliers/${createdSupplierId}/contracts`)
        .set('Authorization', `Bearer ${accessToken}`)
        .field('contractsMetadata', JSON.stringify(contractsMetadata))
        .attach('files', tempFilePath);
    } finally {
      fs.unlinkSync(tempFilePath);
    }
    expect(createResponse.status).toBe(201);
    expect(Array.isArray(createResponse.body)).toBe(true);
    expect(createResponse.body.length).toBeGreaterThan(0);
    const createdContract = createResponse.body[0];
    // GET contracts
    const listResponse = await request(app.getHttpServer())
      .get(`/core/suppliers/${createdSupplierId}/contracts`)
      .set('Authorization', `Bearer ${accessToken}`);
    expect(listResponse.status).toBe(200);
    expect(Array.isArray(listResponse.body)).toBe(true);
    expect(listResponse.body.length).toBeGreaterThan(0);
    const found = listResponse.body.find((c: any) =>
      c.versions && c.versions[0] && c.versions[0].filePath.includes(contractIdentifier)
    );
    expect(found).toBeDefined();
    expect(found.uuid).toBe(createdContract.uuid);
  });

  it('should create and patch supplier contract', async () => {
    const { accessToken, createdSupplierId } = await createSupplierAndToken(app);
    const contractIdentifier = 'contract-e2e-patch.pdf';
    const tempFilePath = path.join(__dirname, contractIdentifier);
    fs.writeFileSync(tempFilePath, 'fake contract content for patch');
    const contractsMetadata = [
      {
        contractIdentifier,
        entityType: 'SUPPLIER',
        entityActualUuid: createdSupplierId,
        contractType: 'CERT_PLATFORM',
        signed: false,
        expirationDate: '2025-12-31',
      },
    ];
    let createResponse;
    try {
      createResponse = await request(app.getHttpServer())
        .post(`/core/suppliers/${createdSupplierId}/contracts`)
        .set('Authorization', `Bearer ${accessToken}`)
        .field('contractsMetadata', JSON.stringify(contractsMetadata))
        .attach('files', tempFilePath);
    } finally {
      fs.unlinkSync(tempFilePath);
    }
    expect(createResponse.status).toBe(201);
    expect(Array.isArray(createResponse.body)).toBe(true);
    expect(createResponse.body.length).toBeGreaterThan(0);
    const createdContract = createResponse.body[0];
    const contractUuid = createdContract.uuid;
    // PATCH contract
    const newSigned = true;
    const patchPayload = { isSigned: newSigned };
    const patchResponse = await request(app.getHttpServer())
      .patch(`/core/suppliers/${createdSupplierId}/contracts/${contractUuid}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(patchPayload);
    expect(patchResponse.status).toBe(200);
    expect(patchResponse.body).toHaveProperty('uuid', contractUuid);
    expect(patchResponse.body.signed).toBe(newSigned);
  });

  it('should create and delete supplier contract', async () => {
    const { accessToken, createdSupplierId } = await createSupplierAndToken(app);
    const contractIdentifier = 'contract-e2e-delete.pdf';
    const tempFilePath = path.join(__dirname, contractIdentifier);
    fs.writeFileSync(tempFilePath, 'fake contract content for delete');
    const contractsMetadata = [
      {
        contractIdentifier,
        entityType: 'SUPPLIER',
        entityActualUuid: createdSupplierId,
        contractType: 'CERT_PLATFORM',
        signed: false,
        expirationDate: '2025-12-31',
      },
    ];
    let createResponse;
    try {
      createResponse = await request(app.getHttpServer())
        .post(`/core/suppliers/${createdSupplierId}/contracts`)
        .set('Authorization', `Bearer ${accessToken}`)
        .field('contractsMetadata', JSON.stringify(contractsMetadata))
        .attach('files', tempFilePath);
    } finally {
      fs.unlinkSync(tempFilePath);
    }
    expect(createResponse.status).toBe(201);
    expect(Array.isArray(createResponse.body)).toBe(true);
    expect(createResponse.body.length).toBeGreaterThan(0);
    const createdContract = createResponse.body[0];
    const contractUuid = createdContract.uuid;
    // DELETE contract
    const deleteResponse = await request(app.getHttpServer())
      .delete(`/core/suppliers/${createdSupplierId}/contracts/${contractUuid}`)
      .set('Authorization', `Bearer ${accessToken}`);
    expect([200, 204]).toContain(deleteResponse.status);
    // Listar e garantir que não existe mais
    const listResponse = await request(app.getHttpServer())
      .get(`/core/suppliers/${createdSupplierId}/contracts`)
      .set('Authorization', `Bearer ${accessToken}`);
    expect(listResponse.status).toBe(200);
    const found = listResponse.body.find((c: any) => c.uuid === contractUuid);
    expect(found).toBeUndefined();
  });
}); 