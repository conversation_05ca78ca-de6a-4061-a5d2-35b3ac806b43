import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { ICustomerDocumentRepository } from '../../domain/repositories/customer-document.repository.interface';
import { CustomerDocument } from '../../domain/entities/customer-document.entity';
import { CUSTOMER_DOCUMENT_REPOSITORY } from '../../domain/constants/tokens';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { v4 as uuidv4 } from 'uuid';

export interface UpdateCustomerDocumentDto {
  name?: string;
  file?: Express.Multer.File;
}

@Injectable()
export class UpdateCustomerDocumentUseCase {
  constructor(
    @Inject(CUSTOMER_DOCUMENT_REPOSITORY)
    private readonly customerDocumentRepository: ICustomerDocumentRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(id: string, data: UpdateCustomerDocumentDto): Promise<CustomerDocument> {
    const document = await this.customerDocumentRepository.findById(id);

    if (!document) {
      throw new NotFoundException(`Document with id ${id} not found`);
    }

    const updateData: Partial<CustomerDocument> = {};

    // Atualizar nome se fornecido
    if (data.name) {
      updateData.name = data.name;
    }

    // Atualizar arquivo se fornecido
    if (data.file) {
      // Gerar UUID único para o novo arquivo
      const documentUuid = uuidv4();
      
      // Definir o caminho no S3
      const key = `customer/${document.customerUuid}/documents/${documentUuid}/${data.file.originalname}`;
      
      // Upload do novo arquivo para o S3
      await this.storageProvider.upload(data.file.buffer, data.file.mimetype, key);
      
      updateData.url = key;
    }

    const updated = await this.customerDocumentRepository.update(id, updateData);

    return updated;
  }
}
