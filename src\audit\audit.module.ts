import { Module } from '@nestjs/common';
import { AuditPublisherService } from './audit-publisher.service';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AuditInterceptor } from './audit.interceptor';
import { RabbitMQModule } from '@/infrastructure/messaging/rabbitmq/rabbitmq.module';

@Module({
  imports: [RabbitMQModule],
  providers: [
    AuditPublisherService,
    {
      provide: APP_INTERCEPTOR,
      useClass: AuditInterceptor,
    },
  ],
  exports: [AuditPublisherService],
})
export class AuditModule {}
