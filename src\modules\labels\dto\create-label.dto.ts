import { ApiProperty } from '@nestjs/swagger';
import { IsString, MaxLength, IsNotEmpty } from 'class-validator';

export class CreateLabelDto {
  @ApiProperty({ example: 'Financeiro', description: 'Nome da label' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  label: string;

  @ApiProperty({
    example: 'metodtoPgto',
    description: 'ID do componente associado',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  idComponent: string;

  @ApiProperty({ example: 'finance', description: 'Nome do módulo associado' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  modulo: string;
} 