import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  Get,
  Param,
  Query,
  Delete,
  HttpCode,
  Patch,
  UseInterceptors,
  UploadedFiles,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiConsumes,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../core/domain/role.enum';
import { SupplierService } from '../services/supplier.service';
import { CreateSupplierDto } from '../dto/create-supplier.dto';
import {
  SupplierResponseDto,
  ContactResponseDto,
} from '../dto/supplier-response.dto';

import { PaginatedSuppliersResponseDto } from '../dto/paginated-suppliers-response.dto';
import { UpdateSupplierDto } from '../dto/update-supplier.dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { DocumentResponseDto } from '../../documents/infrastructure/dto/document-response.dto';
import { ContractResponseDto } from '../../documents/infrastructure/dto/contract-response.dto';
import { UploadSupplierDocumentsDto } from '../dto/upload-supplier-documents.dto';
import {
  CreateContractsDto,
  SupplierContractApiBodyDto,
  SupplierContractTextFormDataDto,
  SupplierContractSignPatchDto,
} from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { Contract } from '@/modules/documents/domain/entities/contract.entity';
import { ServiceItemDto } from '../dto/create-service.dto';
import { ServiceResponseDto } from '../dto/service-response.dto';
import { CreateServiceUseCase } from '../../../core/application/use-cases/service/create-service.use-case';
import { ListServicesUseCase } from '../../../core/application/use-cases/service/list-services.use-case';
import { EntityType } from '../../../core/domain/service/enums/entity-type.enum';
import { ValidateSupplierActivationUseCase } from '../../../core/application/use-cases/supplier/validate-supplier-activation.use-case';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

@ApiTags('Suppliers')
@Controller('core/suppliers')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SupplierController {
  constructor(
    private readonly supplierService: SupplierService,
    private readonly createServiceUseCase: CreateServiceUseCase,
    private readonly listServicesUseCase: ListServicesUseCase,
    private readonly validateSupplierActivationUseCase: ValidateSupplierActivationUseCase,
  ) { }

  @Get()
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'List suppliers' })
  @ApiResponse({
    status: 200,
    description: 'Lista de fornecedores retornada com sucesso.',
    type: PaginatedSuppliersResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Parâmetros inválidos.' })
  @ApiResponse({ status: 401, description: 'Sem autorização.' })
  @ApiResponse({ status: 403, description: 'Sem permissão.' })
  async listSuppliers(@Query() query: Record<string, unknown>): Promise<unknown> {
    return this.supplierService.listSuppliers(query);
  }

  @Get(':uuid/contracts')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER, Role.SUPPLIER)
  @ApiOperation({ summary: 'List supplier contracts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID', 
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de contratos do fornecedor retornada com sucesso.',
    type: [ContractResponseDto],
  })
  async listSupplierContracts(
    @Param('uuid') uuid: string,
  ): Promise<ContractResponseDto[]> {
    const contracts = await this.supplierService.listSupplierContracts(uuid);
    return contracts.map(contract => ContractResponseDto.fromEntity(contract));
  }

  @Post(':uuid/contracts')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Criar contratos para o fornecedor e fazer upload dos arquivos.',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Fornecedor',
    type: String,
  })
  @ApiBody({
    description:
      "Dados para criação de contratos. 'files' são os arquivos e 'contractsMetadata' é uma string JSON com os metadados.", // Corrigido: Removidas barras invertidas extras
    type: SupplierContractApiBodyDto, // Mantém o DTO completo para o Swagger
  })
  @ApiResponse({
    status: 201,
    description: 'Contratos criados com sucesso.',
    type: Array,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiResponse({ status: 403, description: 'Acesso negado.' })
  async createSupplierContracts(
    @Param('uuid') supplierUuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() formData: SupplierContractTextFormDataDto, // Alterado para o DTO que contém apenas os campos de texto
    @Request() req: AuthenticatedRequest,
  ): Promise<Contract[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    let contractsMetadataArray: unknown;
    try {
      contractsMetadataArray = JSON.parse(formData.contractsMetadata);
    } catch (_error) {
      throw new Error(
        'O campo contractsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(contractsMetadataArray) ||
      files.length !== contractsMetadataArray.length
    ) {
      throw new Error(
        'O número de metadatos de contrato não corresponde ao número de arquivos enviados.',
      );
    }

    const processedContractsData = (contractsMetadataArray as unknown[]).map((meta) => ({
      ...(meta as Record<string, unknown>),
      entityUuid: supplierUuid,
      uploadedBy: req.user.id,
    }));

    const createContractsDto: CreateContractsDto = {
      contracts: processedContractsData as never,
    };

    return this.supplierService.createSupplierContract(
      supplierUuid,
      files,
      req.user.id,
      createContractsDto,
    );
  }

  @Delete(':uuid/contracts/:contractUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Remover contrato do fornecedor' })
  @ApiParam({ name: 'uuid', required: true, description: 'UUID do Fornecedor', type: String })
  @ApiParam({ name: 'contractUuid', required: true, description: 'UUID do Contrato', type: String })
  @ApiResponse({ status: 204, description: 'Contrato removido com sucesso.' })
  @ApiResponse({ status: 404, description: 'Contrato não encontrado para este fornecedor.' })
  @HttpCode(204)
  async deleteSupplierContract(
    @Param('uuid') supplierUuid: string,
    @Param('contractUuid') contractUuid: string,
  ): Promise<void> {
    await this.supplierService.deleteSupplierContract(supplierUuid, contractUuid);
  }

  @Patch(':uuid/contracts/:contractUuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Assinar ou rejeitar contrato do fornecedor' })
  @ApiParam({ name: 'uuid', required: true, description: 'UUID do Fornecedor', type: String })
  @ApiParam({ name: 'contractUuid', required: true, description: 'UUID do Contrato', type: String })
  @ApiBody({ description: 'Campos para assinatura/rejeição do contrato', type: SupplierContractSignPatchDto })
  @ApiResponse({ status: 200, description: 'Contrato atualizado com sucesso.', type: ContractResponseDto })
  @ApiResponse({ status: 404, description: 'Contrato não encontrado para este fornecedor.' })
  async patchSupplierContract(
    @Param('uuid') supplierUuid: string,
    @Param('contractUuid') contractUuid: string,
    @Body() patchDto: SupplierContractSignPatchDto,
  ): Promise<ContractResponseDto> {
    const contract = await this.supplierService.updateSupplierContract(supplierUuid, contractUuid, patchDto);
    return ContractResponseDto.fromEntity(contract);
  }

  @Get('classifications')
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'List supplier classifications' })
  @ApiResponse({
    status: 200,
    description:
      'Lista de classificações de fornecedores retornada com sucesso.',
    schema: {
      example: ['CORE', 'GENERAL'],
      type: 'array',
      items: { type: 'string' },
    },
  })
  @ApiResponse({ status: 401, description: 'Sem autorização.' })
  @ApiResponse({ status: 403, description: 'Sem permissão.' })
  async listClassifications(): Promise<string[]> {
    return ['CORE', 'GENERAL'];
  }

  @Get('types')
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'List supplier types' })
  @ApiResponse({
    status: 200,
    description: 'Lista de tipos de fornecedores retornada com sucesso.',
    schema: {
      example: ['BANK', 'GAME', 'SPORTSBOOK', 'KYC', 'OTHER'],
      type: 'array',
      items: { type: 'string' },
    },
  })
  @ApiResponse({ status: 401, description: 'Sem autorização.' })
  @ApiResponse({ status: 403, description: 'Sem permissão.' })
  async listSupplierTypes(): Promise<string[]> {
    return this.supplierService.getSupplierTypes();
  }

  @Get(':uuid')
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Get supplier by UUID' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'O fornecedor foi listado com sucesso.',
    type: SupplierResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Sem autorização.' })
  @ApiResponse({ status: 403, description: 'Sem premição.' })
  @ApiResponse({ status: 404, description: 'Fornecedor não encontrado.' })
  async listSupplierByUuid(
    @Param('uuid') uuid: string,
  ): Promise<SupplierResponseDto> {
    return this.supplierService.listSupplierByUuid(uuid);
  }

  @Post()
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Create supplier' })
  @ApiBody({ type: CreateSupplierDto })
  @ApiResponse({
    status: 201,
    description: 'O fornecedor foi criado com sucesso.',
    type: SupplierResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({
    status: 409,
    description: 'Já existe um fornecedor com este CNPJ.',
  })
  async create(
    @Body() createSupplierDto: CreateSupplierDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<SupplierResponseDto> {
    return this.supplierService.createSupplier(createSupplierDto, req.user.id);
  }

  @Delete(':uuid')
  @HttpCode(204)
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Delete supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Fornecedor deletado com sucesso.',
    schema: {
      example: {
        status: 'success',
        message: 'Fornecedor excluído com sucesso',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Sem autorização.' })
  @ApiResponse({ status: 403, description: 'Sem permissão.' })
  @ApiResponse({ status: 404, description: 'Fornecedor não encontrado.' })
  async delete(@Param('uuid') uuid: string): Promise<void> {
    return this.supplierService.deleteSupplier(uuid);
  }

  @Patch(':uuid')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Update supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: UpdateSupplierDto })
  @ApiResponse({
    status: 200,
    description: 'Fornecedor atualizado com sucesso.',
    type: SupplierResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 401, description: 'Sem autorização.' })
  @ApiResponse({ status: 403, description: 'Sem permissão.' })
  @ApiResponse({ status: 404, description: 'Fornecedor não encontrado.' })
  @ApiResponse({ status: 409, description: 'CNPJ já cadastrado.' })
  async updateSupplierPatch(
    @Param('uuid') uuid: string,
    @Body() dto: UpdateSupplierDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<SupplierResponseDto> {
    return this.supplierService.updateSupplier(uuid, dto, req.user.id);
  }

  @Post(':uuid/documents')
  @Roles(Role.ADMIN)
  @UseInterceptors(FilesInterceptor('documents'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload documents for supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiBody({ description: 'Files to upload', type: UploadSupplierDocumentsDto })
  @ApiResponse({
    status: 201,
    description: 'Documentos enviados com sucesso.',
    type: [DocumentResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos.' })
  async uploadDocuments(
    @Param('uuid') uuid: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body('documentsMetadata') documentsMetadata: string,
    @Request() req: AuthenticatedRequest,
  ): Promise<DocumentResponseDto[]> {
    if (!files || files.length === 0) {
      throw new Error('Pelo menos um arquivo deve ser enviado.');
    }

    const documents = await this.supplierService.uploadSupplierDocuments(
      uuid,
      files,
      documentsMetadata,
      req.user.id,
    );
    return documents.map(document => DocumentResponseDto.fromEntity(document));
  }

  @Get(':uuid/documents')
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'List documents for supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de documentos do fornecedor retornada com sucesso.',
    type: [DocumentResponseDto],
  })
  async listSupplierDocuments(
    @Param('uuid') uuid: string,
  ): Promise<DocumentResponseDto[]> {
    const documents = await this.supplierService.listSupplierDocuments(uuid);
    return documents.map(document => DocumentResponseDto.fromEntity(document));
  }

  @Get(':uuid/contacts')
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'List supplier contacts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de contatos do fornecedor retornada com sucesso.',
    type: [ContactResponseDto],
  })
  async listSupplierContacts(
    @Param('uuid') uuid: string,
  ): Promise<{ contacts: ContactResponseDto[] }> {
    return this.supplierService.listSupplierContacts(uuid);
  }

  @Post(':uuid/contacts')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Create supplier contacts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          contact: { type: 'string' },
          type: { type: 'string' },
          area: { type: 'string' },
          responsible: { type: 'string' },
        },
        required: ['contact', 'type', 'area', 'responsible'],
        example: {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Finance',
          responsible: 'Maria Silva',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Contatos criados com sucesso.',
    type: [ContactResponseDto],
  })
  async createSupplierContact(
    @Param('uuid') uuid: string,
    @Body()
    contactsData: Array<{
      contact: string;
      type: string;
      area: string;
      responsible: string;
    }>,
  ): Promise<ContactResponseDto[]> {
    return this.supplierService.createSupplierContacts(uuid, contactsData);
  }

  @Patch(':uuid/contacts/:contactUuid')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Update supplier contacts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        contact: { type: 'string' },
        type: { type: 'string' },
        area: { type: 'string' },
        responsible: { type: 'string' },
      },
      example: {
        contact: '<EMAIL>',
        type: 'email',
        area: 'Finance',
        responsible: 'Maria Silva',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Contatos atualizado com sucesso.',
    type: ContactResponseDto,
  })
  async updateSupplierContact(
    @Param('uuid') _uuid: string,
    @Param('contactUuid') contactUuid: string,
    @Body()
    body: {
      contact: string;
      type: string;
      area: string;
      responsible: string;
    },
  ): Promise<ContactResponseDto> {
    return await this.supplierService.updateSupplierContacts(contactUuid, body);
  }

  @Delete(':uuid/contacts/:contactUuid')
  @Roles(Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Delete supplier contacts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Supplier UUID',
    type: String,
  })
  @ApiResponse({
    status: 204,
    description: 'Contato deletado com sucesso.',
  })
  async deleteSupplierContact(
    @Param('uuid') _uuid: string,
    @Param('contactUuid') contactUuid: string,
  ): Promise<void> {
    await this.supplierService.deleteSupplierContacts(contactUuid);
  }

  @Post(':uuid/services')
  @Roles(Role.USER, Role.ADMIN)
  @ApiOperation({ summary: 'Criar serviços para supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Supplier',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
            description: 'Tipo do serviço',
            example: 'Consultoria',
          },
          rate: {
            type: 'string',
            description: 'Taxa ou valor',
            example: 'R$ 150/hora',
          },
          description: {
            type: 'string',
            description: 'Descrição do serviço',
            example: 'Consultoria em TI',
          },
        },
        required: ['type', 'rate', 'description'],
      },
      example: [
        {
          type: 'Consultoria',
          rate: 'R$ 150/hora',
          description: 'Consultoria em TI',
        },
        {
          type: 'Desenvolvimento',
          rate: 'R$ 100/hora',
          description: 'Desenvolvimento de software',
        },
      ],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Serviços criados com sucesso.',
    type: [ServiceResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos.' })
  @ApiResponse({ status: 404, description: 'Supplier não encontrado.' })
  async createSupplierService(
    @Param('uuid') uuid: string,
    @Body() servicesData: ServiceItemDto[],
    @Request() req: AuthenticatedRequest,
  ): Promise<ServiceResponseDto[]> {
    // Verificar se o supplier existe antes de criar os services
    const supplier = await this.supplierService.listSupplierByUuid(uuid);
    if (!supplier) {
      throw new NotFoundException('Supplier not found');
    }

    const createdServices = await this.createServiceUseCase.executeMultiple(
      servicesData,
      EntityType.SUPPLIER,
      uuid,
      req.user.id,
    );

    try {
      await this.validateSupplierActivationUseCase.execute(uuid, req.user.id);
    } catch (error) {
      console.error(
        '[SupplierController] Erro ao tentar ativar supplier após criar serviços:',
        error,
      );
    }

    return createdServices.map((service) =>
      ServiceResponseDto.fromEntity(service),
    );
  }

  @Get(':uuid/services')
  @Roles(Role.USER, Role.ADMIN, Role.SUPPLIER_VIEWER)
  @ApiOperation({ summary: 'Listar serviços do supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Supplier',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de serviços do supplier retornada com sucesso.',
    type: [ServiceResponseDto],
  })
  async listSupplierServices(
    @Param('uuid') uuid: string,
  ): Promise<ServiceResponseDto[]> {
    const result = await this.listServicesUseCase.execute({
      limit: 100,
      offset: 0,
      entityUuid: uuid,
      entityType: EntityType.SUPPLIER,
    });

    return result.items.map(ServiceResponseDto.fromEntity);
  }

  @Get(':uuid/activation-status')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Verificar status de ativação do supplier' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Supplier',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Status de ativação retornado com sucesso.',
    schema: {
      type: 'object',
      properties: {
        hasContract: {
          type: 'boolean',
          description: 'Se tem pelo menos um contrato',
        },
        hasDocument: {
          type: 'boolean',
          description: 'Se tem pelo menos um documento',
        },
        hasService: {
          type: 'boolean',
          description: 'Se tem pelo menos um serviço',
        },
        hasContact: {
          type: 'boolean',
          description: 'Se tem pelo menos um contato',
        },
        canActivate: { type: 'boolean', description: 'Se pode ser ativado' },
        currentStatus: {
          type: 'string',
          description: 'Status atual do supplier',
        },
      },
    },
  })
  async getSupplierActivationStatus(@Param('uuid') uuid: string): Promise<{
    hasContract: boolean;
    hasDocument: boolean;
    hasService: boolean;
    hasContact: boolean;
    canActivate: boolean;
    currentStatus: string;
  }> {
    return this.validateSupplierActivationUseCase.validateRequirements(uuid);
  }

  @Patch(':uuid/activate')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Tentar ativar o supplier automaticamente' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Supplier',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Resultado da tentativa de ativação.',
    schema: {
      type: 'object',
      properties: {
        activated: {
          type: 'boolean',
          description: 'Se o supplier foi ativado',
        },
        message: { type: 'string', description: 'Mensagem explicativa' },
        requirements: {
          type: 'object',
          properties: {
            hasContract: { type: 'boolean' },
            hasDocument: { type: 'boolean' },
            hasService: { type: 'boolean' },
            hasContact: { type: 'boolean' },
          },
        },
      },
    },
  })
  async activateSupplier(
    @Param('uuid') uuid: string,
    @Request() req: AuthenticatedRequest,
  ): Promise<{
    activated: boolean;
    message: string;
    requirements: {
      hasContract: boolean;
      hasDocument: boolean;
      hasService: boolean;
      hasContact: boolean;
    };
  }> {
    const requirements =
      await this.validateSupplierActivationUseCase.validateRequirements(uuid);

    if (!requirements.canActivate) {
      return {
        activated: false,
        message:
          'Supplier não pode ser ativado. Faltam requisitos obrigatórios.',
        requirements: {
          hasContract: requirements.hasContract,
          hasDocument: requirements.hasDocument,
          hasService: requirements.hasService,
          hasContact: requirements.hasContact,
        },
      };
    }

    const activated = await this.validateSupplierActivationUseCase.execute(
      uuid,
      req.user.id,
    );

    return {
      activated,
      message: activated
        ? 'Supplier ativado com sucesso!'
        : 'Supplier já estava ativo ou não pôde ser ativado.',
      requirements: {
        hasContract: requirements.hasContract,
        hasDocument: requirements.hasDocument,
        hasService: requirements.hasService,
        hasContact: requirements.hasContact,
      },
    };
  }

  @Get(':uuid/debug-data')
  @Roles(Role.ADMIN)
  @ApiOperation({
    summary: 'Debug - Verificar dados reais do supplier no banco',
  })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Supplier',
    type: String,
  })
  async debugSupplierData(@Param('uuid') uuid: string): Promise<unknown> {
    console.log(
      `[SupplierController] Debug - Verificando dados para supplier: ${uuid}`,
    );

    try {
      const result =
        await this.validateSupplierActivationUseCase.validateRequirements(uuid);
      console.log(
        `[SupplierController] Debug - Resultado da validação:`,
        result,
      );
      return result;
    } catch (error) {
      console.error(`[SupplierController] Debug - Erro:`, error);
      throw error;
    }
  }
}
