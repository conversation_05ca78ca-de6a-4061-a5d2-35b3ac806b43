import { Controller, Get, HttpStatus, Res } from '@nestjs/common';
import { Response } from 'express';
import { HealthService } from './health.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get('live')
  @ApiOperation({
    summary: 'Verifica se a aplicação está viva (liveness probe)',
  })
  @ApiResponse({
    status: 200,
    description: 'Aplicação está viva',
    schema: {
      example: { status: 'ok' },
    },
  })
  getLiveness(@Res() res: Response) {
    return res.status(HttpStatus.OK).json({ status: 'ok' });
  }

  @Get('ready')
  @ApiOperation({
    summary: 'Verifica se a aplicação está pronta (readiness probe)',
  })
  @ApiResponse({
    status: 200,
    description: 'Aplicação está pronta e dependências estão OK',
    schema: {
      example: { status: 'ready' },
    },
  })
  @ApiResponse({
    status: 503,
    description: 'Aplicação não está pronta',
    schema: {
      example: { status: 'unavailable' },
    },
  })
  async getReadiness(@Res() res: Response) {
    const ready = await this.healthService.isReady();
    return ready
      ? res.status(HttpStatus.OK).json({ status: 'ready' })
      : res
          .status(HttpStatus.SERVICE_UNAVAILABLE)
          .json({ status: 'unavailable' });
  }
}
