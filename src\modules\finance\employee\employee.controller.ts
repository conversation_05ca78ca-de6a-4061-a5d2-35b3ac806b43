import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Get,
  Query,
  Patch,
  Delete,
  Param,
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { EmployeeService } from './employee.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { EmployeeResponseDto } from './dto/employee-response.dto';
import { ListEmployeeQueryDto } from './dto/list-employee-query.dto';
import { PaginatedEmployeeResponseDto } from './dto/paginated-employee-response.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { EmployeeArchiveResponseDto } from './dto/employee-archive-response.dto';
import { EmployeeArchivesResponseDto } from './dto/employee-archives-response.dto';
import { EmployeeArchiveDeleteResponseDto } from './dto/employee-archive-delete-response.dto';
import { PersonalDocumentsResponseDto } from './dto/employee-personal-documents-response.dto';
import { PersonalDocumentResponseDto } from './dto/employee-personal-document-response.dto';

@ApiTags('Employees')
@Controller('core/employees')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) { }

  @Post()
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Create a new employee' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Employee created successfully',
    type: EmployeeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async create(
    @Body() createEmployeeDto: CreateEmployeeDto,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.create(createEmployeeDto);
  }

  @Get()
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'List all employees' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of employees',
    type: PaginatedEmployeeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findAll(
    @Query() query: ListEmployeeQueryDto,
  ): Promise<PaginatedEmployeeResponseDto> {
    return this.employeeService.findAll(query);
  }

  @Get(':uuid/personal-documents/')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'List all personal documents for an employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of personal documents',
    type: PersonalDocumentsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async listPersonalDocuments(@Param('uuid') uuid: string): Promise<PersonalDocumentsResponseDto> {
    return this.employeeService.listPersonalDocuments(uuid);
  }

  @Post(':uuid/personal-documents/:number')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload a personal document for an employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiParam({
    name: 'number',
    description: 'Number of personal document',
    type: String,
  })
  @ApiBody({
    description: 'File to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload for the employee personal document',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Personal document uploaded successfully',
    type: PersonalDocumentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing file',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async uploadPersonalDocument(
    @Param('uuid') uuid: string,
    @Param('number') number: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() req,
  ): Promise<PersonalDocumentResponseDto> {
    const userId = req.user.sub;
    return this.employeeService.uploadPersonalDocument(uuid, number, file);
  }

  @Delete(':uuid/personal-documents/:number')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Delete a personal document for an employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiParam({
    name: 'number',
    description: 'Number of personal document',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Personal document deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Personal document not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async deletePersonalDocument(
    @Param('uuid') uuid: string,
    @Param('number') number: string,
  ){
    await this.employeeService.deletePersonalDocument(uuid, number);
    return {
      message: 'Personal document deleted successfully',
    } 
  }
  @Get(':uuid')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Get employee by UUID' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employee found',
    type: EmployeeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findByUuid(@Param('uuid') uuid: string): Promise<EmployeeResponseDto> {
    return this.employeeService.findByUuid(uuid);
  }

  @Patch(':uuid')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Update employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiBody({ type: UpdateEmployeeDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employee updated successfully',
    type: EmployeeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async update(
    @Param('uuid') uuid: string,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @Req() req,
  ): Promise<EmployeeResponseDto> {
    const userId = req.user.sub;
    return this.employeeService.update(uuid, updateEmployeeDto, userId);
  }

  @Delete(':uuid')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Employee deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async delete(@Param('uuid') uuid: string): Promise<void> {
    await this.employeeService.delete(uuid);
  }

  @Post(':uuid/archives')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Create a new archive for an employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiBody({
    description: 'File to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload for the employee archive',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Archive created successfully',
    type: EmployeeArchiveResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing file',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async createArchive(
    @Param('uuid') uuid: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() req,
  ): Promise<EmployeeArchiveResponseDto> {
    const userId = req.user.sub;
    return this.employeeService.createArchive(uuid, file, userId);
  }

  @Get(':uuid/archives')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Get all archives for an employee' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of archives',
    type: EmployeeArchivesResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findArchives(@Param('uuid') uuid: string): Promise<EmployeeArchivesResponseDto> {
    return this.employeeService.findArchives(uuid);
  }

  @Get(':uuid/archives/:id')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Get an archive by ID' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiParam({
    name: 'id',
    description: 'Archive ID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Archive found',
    type: EmployeeArchiveResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Archive not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findArchive(@Param('uuid') uuid: string, @Param('id') id: string): Promise<EmployeeArchiveResponseDto> {
    return this.employeeService.findArchive(id);
  }

  @Delete(':uuid/archives/:id')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @ApiOperation({ summary: 'Delete an archive by ID' })
  @ApiParam({
    name: 'uuid',
    description: 'Employee UUID',
    type: String,
  })
  @ApiParam({
    name: 'id',
    description: 'Archive ID',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Archive deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Archive not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async deleteArchive(@Param('uuid') uuid: string, @Param('id') id: string): Promise<EmployeeArchiveDeleteResponseDto> {
    return this.employeeService.deleteArchive(id);
  }
}
