import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, IsUUID, MinLength, ValidateNested } from 'class-validator';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { ContractType } from '@prisma/client';
import { Type } from 'class-transformer';

export class CreateContractDto {
  @ApiProperty({
    description: 'Identificador único ou nome para o contrato/arquivo dentro do lote.',
    example: 'contrato_servico_principal.pdf',
  })
  @IsString()
  @MinLength(1)
  contractIdentifier: string;

  @ApiProperty({
    description: 'Tipo da entidade à qual o contrato está associado (ex: SUPPLIER).',
    enum: EntityType,
    example: EntityType.SUPPLIER,
  })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID da entidade principal (ex: Fornecedor) à qual este contrato pertence.',
    format: 'uuid'
  })
  @IsUUID()
  entityActualUuid: string;

  @ApiPropertyOptional({
    description: 'Data de expiração do documento (opcional)',
    format: 'date',
    example: '2025-12-31',
  })
  @IsOptional()
  @IsString()
  expirationDate?: string;

  @ApiPropertyOptional({
    description: 'Tipo de contrato',
    enum: ContractType,
    example: ContractType.CERT_PLATFORM,
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  @ApiPropertyOptional({
    description: 'Se o contrato foi assinado',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  signed?: boolean;
}

export class CreateContractsDto {
  @ApiProperty({
    description: 'Lista de metadados dos contratos',
    type: [CreateContractDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateContractDto)
  contracts: CreateContractDto[];
}

export class SupplierContractApiBodyDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    description: "Arquivos dos contratos a serem enviados. Deve haver um arquivo para cada objeto na string JSON 'contractsMetadata'."
  })
  files: any[];

  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada contrato (corresponde à estrutura de CreateContractDto). O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"contractIdentifier": "contrato_principal.pdf", "entityType": "SUPPLIER", "entityActualUuid": "40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4", "contractType": "CERT_PLATFORM", "signed": true, "expirationDate": "2025-12-31"}]'
  })
  @IsString()
  contractsMetadata: string;
}

export class SupplierContractTextFormDataDto {
  @ApiProperty({
    description: "Uma string JSON contendo um array de metadados para cada contrato (corresponde à estrutura de CreateContractDto). O número de objetos neste array deve corresponder ao número de arquivos enviados em 'files'.",
    example: '[{"contractIdentifier": "contrato_principal.pdf", "entityType": "SUPPLIER", "entityActualUuid": "40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4", "contractType": "CERT_PLATFORM", "signed": true, "expirationDate": "2025-12-31"}]'
  })
  @IsString()
  contractsMetadata: string;
}

// export class SupplierContractPatchDto {
//   @ApiPropertyOptional({ description: 'Identificador do contrato', example: 'contrato.pdf' })
//   @IsOptional()
//   @IsString()
//   contractIdentifier?: string;

//   @ApiPropertyOptional({ description: 'UUID da entidade principal', format: 'uuid' })
//   @IsOptional()
//   @IsUUID()
//   entityActualUuid?: string;

//   @ApiPropertyOptional({ description: 'Data de expiração', format: 'date', example: '2025-12-31' })
//   @IsOptional()
//   @IsString()
//   expirationDate?: string;

//   @ApiPropertyOptional({ description: 'Tipo de contrato', enum: ContractType, example: ContractType.CERT_PLATFORM })
//   @IsOptional()
//   @IsEnum(ContractType)
//   contractType?: ContractType;

//   @ApiPropertyOptional({ description: 'Se o contrato foi assinado', example: true })
//   @IsOptional()
//   @IsBoolean()
//   signed?: boolean;

//   @ApiPropertyOptional({ description: 'Status do contrato', example: 'PENDING' })
//   @IsOptional()
//   @IsString()
//   status?: string;
// }

// export class SupplierContractPatchFormDataDto {
//   @ApiProperty({ description: 'String JSON com os campos a serem atualizados', example: '{"contractIdentifier": "novo_nome.pdf"}' })
//   @IsString()
//   contractMetadata: string;
// }

export class SupplierContractSignPatchDto {
  @ApiProperty({ description: 'Indica se o contrato foi assinado', type: Boolean })
  @IsBoolean()
  isSigned: boolean;
}