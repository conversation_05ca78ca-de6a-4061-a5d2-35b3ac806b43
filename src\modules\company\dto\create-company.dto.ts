import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsEnum,
  IsObject,
  MinLength,
  MaxLength,
  Matches,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CompanyStatus } from '../../../core/domain/company.entity';

class CompanyAddressDto {
  @ApiProperty({
    example: 'Av. Paulista, 1000',
    description: 'Endereço completo da empresa',
  })
  @IsString({ message: 'O endereço deve ser uma string válida' })
  @MinLength(3, { message: 'O endereço deve ter no mínimo 3 caracteres' })
  @MaxLength(100, { message: 'O endereço deve ter no máximo 100 caracteres' })
  street: string;

  @ApiProperty({
    example: 'São Paulo',
    description: 'Cidade onde a empresa está localizada',
  })
  @IsString({ message: 'A cidade deve ser uma string válida' })
  @MinLength(2, { message: 'A cidade deve ter no mínimo 2 caracteres' })
  @MaxLength(50, { message: 'A cidade deve ter no máximo 50 caracteres' })
  city: string;

  @ApiProperty({
    example: '01310-100',
    description: 'CEP da empresa no formato 00000-000',
  })
  @IsString({ message: 'O CEP deve ser uma string válida' })
  @Matches(/^\d{5}-\d{3}$/, {
    message: 'O CEP deve estar no formato 00000-000',
  })
  zipCode: string;

  @ApiProperty({
    example: 'SP',
    description: 'UF (Unidade Federativa) da empresa',
  })
  @IsString({ message: 'A UF deve ser uma string válida' })
  @MinLength(2, { message: 'A UF deve ter 2 caracteres' })
  @MaxLength(2, { message: 'A UF deve ter 2 caracteres' })
  state: string;
}

export class CreateCompanyDto {
  @ApiProperty({
    example: 'Acme Ltda',
    description: 'Nome completo da empresa',
  })
  @IsString({ message: 'O nome deve ser uma string válida' })
  @MinLength(3, { message: 'O nome deve ter no mínimo 3 caracteres' })
  @MaxLength(100, { message: 'O nome deve ter no máximo 100 caracteres' })
  razaoSocial: string;

  @ApiProperty({
    example: '12345678000195',
    description: 'CNPJ da empresa (apenas números)',
  })
  @IsString({ message: 'O CNPJ deve ser uma string válida' })
  @Matches(/^\d{14}$/, {
    message: 'O CNPJ deve conter exatamente 14 dígitos numéricos',
  })
  cnpj: string;

  @ApiProperty({
    description: 'Endereço completo da empresa',
  })
  @IsObject({ message: 'O endereço deve ser um objeto válido' })
  @ValidateNested()
  @Type(() => CompanyAddressDto)
  address: CompanyAddressDto;

  @ApiProperty({
    example: '(11) 98765-4321',
    description: 'Telefone da empresa no formato (00) 00000-0000',
  })
  @IsString({ message: 'O telefone deve ser uma string válida' })
  @Matches(/^\(\d{2}\) \d{4,5}-\d{4}$/, {
    message: 'O telefone deve estar no formato (00) 00000-0000',
  })
  phone: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'E-mail de contato da empresa',
  })
  @IsEmail({}, { message: 'E-mail inválido' })
  email: string;

  @ApiProperty({
    enum: CompanyStatus,
    example: CompanyStatus.ACTIVE,
    description: 'Status da empresa (active = ativa, inactive = inativa)',
  })
  @IsEnum(CompanyStatus, {
    message:
      'Status inválido. Use "active" para ativa ou "inactive" para inativa',
  })
  status: CompanyStatus;
}
