import { Test, TestingModule } from '@nestjs/testing';
import { FileReaderService } from './file-reader.service';
import {
  UnsupportedFileTypeException,
  FileCorruptedException,
  MissingRequiredColumnsException,
  FileProcessingException,
} from '../exceptions/file-reader.exceptions';

describe('FileReaderService', () => {
  let service: FileReaderService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FileReaderService],
    }).compile();

    service = module.get<FileReaderService>(FileReaderService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('readFile', () => {
    it('should parse CSV file successfully', async () => {
      const csvContent =
        'name,email,age\nJohn <PERSON>e,<EMAIL>,30\n<PERSON><PERSON>,<EMAIL>,25';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const result = await service.readFile(mockFile);

      expect(result.totalRows).toBe(2);
      expect(result.columns).toEqual(['name', 'email', 'age']);
      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toHaveProperty('name', 'John Doe');
      expect(result.data[0]).toHaveProperty('email', '<EMAIL>');
      expect(result.data[0]).toHaveProperty('age', '30');
      expect(result.data[1]).toHaveProperty('name', 'Jane Smith');
      expect(result.data[1]).toHaveProperty('email', '<EMAIL>');
      expect(result.data[1]).toHaveProperty('age', '25');
    });

    it('should validate required columns', async () => {
      const csvContent = 'name,email\nJohn Doe,<EMAIL>';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      await expect(
        service.readFile(mockFile, {
          requiredColumns: ['name', 'email', 'age'],
        }),
      ).rejects.toThrow(MissingRequiredColumnsException);
    });

    it('should throw UnsupportedFileTypeException for unsupported file types', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.txt',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: 100,
        buffer: Buffer.from('some content'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      await expect(service.readFile(mockFile)).rejects.toThrow(
        UnsupportedFileTypeException,
      );
    });

    it('should handle CSV with custom delimiter', async () => {
      const csvContent = 'name;email;age\nJohn Doe;<EMAIL>;30';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const result = await service.readFile(mockFile, { delimiter: ';' });

      expect(result.totalRows).toBe(1);
      expect(result.columns).toEqual(['name', 'email', 'age']);
      expect(result.data[0]).toHaveProperty('name', 'John Doe');
      expect(result.data[0]).toHaveProperty('email', '<EMAIL>');
      expect(result.data[0]).toHaveProperty('age', '30');
    });

    it('should trim values by default', async () => {
      const csvContent = 'name,email\n  John Doe  ,  <EMAIL>  ';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const result = await service.readFile(mockFile);

      expect(result.data[0]).toHaveProperty('name', 'John Doe');
      expect(result.data[0]).toHaveProperty('email', '<EMAIL>');
    });

    it('should skip empty lines by default', async () => {
      const csvContent =
        'name,email\nJohn Doe,<EMAIL>\n\n\nJane Smith,<EMAIL>';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const result = await service.readFile(mockFile);

      // O csv-parser pode processar linhas vazias de forma diferente
      expect(result.totalRows).toBeGreaterThanOrEqual(2);
      expect(result.data.filter((row) => row.name && row.email)).toHaveLength(
        2,
      );

      const validRows = result.data.filter((row) => row.name && row.email);
      expect(validRows[0]).toHaveProperty('name', 'John Doe');
      expect(validRows[0]).toHaveProperty('email', '<EMAIL>');
      expect(validRows[1]).toHaveProperty('name', 'Jane Smith');
      expect(validRows[1]).toHaveProperty('email', '<EMAIL>');
    });
  });

  describe('getFileInfo', () => {
    it('should return file information for CSV', async () => {
      const csvContent = 'name,email,age\nJohn Doe,<EMAIL>,30';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const result = await service.getFileInfo(mockFile);

      expect(result.filename).toBe('test.csv');
      expect(result.size).toBe(csvContent.length);
      expect(result.type).toBe('csv');
      expect(result.columns).toEqual(['name', 'email', 'age']);
    });

    it('should handle files without extension', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: 100,
        buffer: Buffer.from('content'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      await expect(service.getFileInfo(mockFile)).rejects.toThrow(
        UnsupportedFileTypeException,
      );
    });
  });
});
