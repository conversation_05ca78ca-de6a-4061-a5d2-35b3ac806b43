import {
  Certificate as PrismaCertificate,
  CertificateCategory as PrismaCertificateCategory,
  CertificateType as PrismaCertificateType,
} from '@prisma/client';
import {
  Certificate,
  CertificateProps,
} from '../../domain/entities/certificate.entity';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';

export class CertificateMapper {
  static toDomain(prismaCert: PrismaCertificate): Certificate {
    const props: CertificateProps = {
      id: prismaCert.id,
      customerId: prismaCert.customerId,
      category: prismaCert.category as CertificateCategory,
      type: prismaCert.type as CertificateType,
      fileUrl: prismaCert.fileUrl,
      notes: prismaCert.notes ?? undefined,
      uploadedById: prismaCert.uploadedById,
      createdAt: prismaCert.createdAt,
      updatedAt: prismaCert.updatedAt,
    };
    return Certificate.create(props);
  }

  static toPersistence(domainCert: Certificate): PrismaCertificate {
    return {
      id: domainCert.id,
      customerId: domainCert.customerId,
      category: domainCert.category as PrismaCertificateCategory,
      type: domainCert.type as PrismaCertificateType,
      fileUrl: domainCert.fileUrl,
      notes: domainCert.notes ?? null,
      uploadedById: domainCert.uploadedById,
      createdAt: domainCert.createdAt,
      updatedAt: domainCert.updatedAt,
    };
  }
} 