import {
  PostgreSqlContainer,
  StartedPostgreSqlContainer,
} from '@testcontainers/postgresql';
import { GenericContainer, StartedTestContainer } from 'testcontainers';
import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';

export class TestContainer {
  private static postgresContainer: StartedPostgreSqlContainer;
  private static rabbitmqContainer: StartedTestContainer;
  private static prisma: PrismaClient;

  static async start() {
    // Start PostgreSQL container
    this.postgresContainer = await new PostgreSqlContainer()
      .withDatabase('backoffice_test')
      .withUsername('postgres')
      .withPassword('postgres')
      .withExposedPorts(5432)
      .start();

    // Start RabbitMQ container
    this.rabbitmqContainer = await new GenericContainer('rabbitmq:3-management')
      .withExposedPorts(5672, 15672)
      .withEnvironment({
        RABBITMQ_DEFAULT_USER: 'guest',
        RABBITMQ_DEFAULT_PASS: 'guest',
      })
      .start();

    // Configure database URL
    const databaseUrl = this.postgresContainer.getConnectionUri();
    process.env.DATABASE_URL = databaseUrl;

    // Configure RabbitMQ environment variables
    const rabbitmqPort = this.rabbitmqContainer.getMappedPort(5672);
    process.env.RABBITMQ_HOST = 'localhost';
    process.env.RABBITMQ_PORT = rabbitmqPort.toString();
    process.env.RABBITMQ_USERNAME = 'guest';
    process.env.RABBITMQ_PASSWORD = 'guest';

    // Run Prisma migrations
    execSync('npx prisma migrate deploy', {
      env: { ...process.env, DATABASE_URL: databaseUrl },
    });

    // Initialize Prisma client
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: databaseUrl,
        },
      },
    });

    // Connect to database
    await this.prisma.$connect();
  }

  static async stop() {
    // Disconnect Prisma client
    if (this.prisma) {
      await this.prisma.$disconnect();
    }

    // Stop containers
    if (this.postgresContainer) {
      await this.postgresContainer.stop();
    }
    if (this.rabbitmqContainer) {
      await this.rabbitmqContainer.stop();
    }
  }
}
