import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import {
  CustomerFilterCriteria,
  CustomerListResponse,
  IListCustomersUseCase,
} from '../../domain/use-cases/list-customers.use-case.interface';
import { CustomerStatus } from '@prisma/client';

@Injectable()
export class ListCustomersUseCase implements IListCustomersUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
  ) {}

  async execute(
    criteria: CustomerFilterCriteria,
    limit: number,
    offset: number,
  ): Promise<CustomerListResponse> {
    if (limit < 1 || offset < 0) {
      throw new BadRequestException(
        'Limit must be >= 1 and offset must be >= 0',
      );
    }

    const { customers, total } = await this.customerRepository.listCustomers(
      criteria,
      limit,
      offset,
      criteria.status as CustomerStatus,
    );

    return {
      items: customers,
      limit,
      offset,
      total,
    };
  }
}
