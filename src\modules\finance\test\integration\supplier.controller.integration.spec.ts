import { Test, TestingModule } from '@nestjs/testing';
import {
  INestApplication,
  ValidationPipe,
  ExecutionContext,
} from '@nestjs/common';
import * as request from 'supertest';
import { SupplierController } from '../../controllers/supplier.controller';
import { SupplierService } from '../../services/supplier.service';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../auth/guards/roles.guard';
import { SupplierResponseDto } from '../../dto/supplier-response.dto';
import { PaginatedSuppliersResponseDto } from '../../dto/paginated-suppliers-response.dto';
import { SupplierStatus } from '@/core/domain/supplier/enums/supplier-status.enum';
import { Request } from 'express';
import { Server } from 'http';

// Define a type for the request with user property
interface RequestWithUser extends Request {
  user: { id: string };
}

describe('SupplierController (Integration)', () => {
  let app: INestApplication;
  let supplierService: SupplierService;
  let httpServer: Server;

  const mockSupplierResponse: SupplierResponseDto = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    cnpj: '12345678901234',
    tradeName: 'Test Trade Name',
    address: {
      street: 'Test Street',
      city: 'Test City',
      zipCode: '12345678',
      state: 'TS',
    },
    contact: {
      email: '<EMAIL>',
      phone: '1234567890',
    },
    status: SupplierStatus.ACTIVE,
    createdAt: '2023-01-01T00:00:00.000Z',
    createdBy: 'user-uuid',
    updatedAt: '2023-01-01T00:00:00.000Z',
    updatedBy: 'user-uuid',
  };

  const mockPaginatedResponse: PaginatedSuppliersResponseDto = {
    items: [mockSupplierResponse],
    total: 1,
    limit: 10,
    offset: 0,
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn().mockImplementation((context: ExecutionContext) => {
      const req = context.switchToHttp().getRequest<Request>();
      // Cast to RequestWithUser to add user property
      (req as RequestWithUser).user = { id: 'user-uuid' };
      return true;
    }),
  };

  const mockRolesGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    const mockSupplierService = {
      listSuppliers: jest.fn().mockResolvedValue(mockPaginatedResponse),
      listSupplierByUuid: jest.fn().mockResolvedValue(mockSupplierResponse),
      createSupplier: jest.fn().mockResolvedValue(mockSupplierResponse),
      deleteSupplier: jest.fn().mockResolvedValue(undefined),
      updateSupplier: jest.fn().mockResolvedValue({
        ...mockSupplierResponse,
        name: 'Updated Supplier',
        updatedAt: '2023-01-02T00:00:00.000Z',
      }),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [SupplierController],
      providers: [
        {
          provide: SupplierService,
          useValue: mockSupplierService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: false,
        skipMissingProperties: true,
      }),
    );
    await app.init();

    supplierService = moduleFixture.get<SupplierService>(SupplierService);
    // Explicitly cast to Server type to avoid 'any' type
    httpServer = app.getHttpServer() as Server;
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /core/suppliers', () => {
    it('should return a paginated list of suppliers', async () => {
      // Create a spy before making the request
      const listSpy = jest.spyOn(supplierService, 'listSuppliers');
      // Using the httpServer variable that's properly typed
      const response = await request(httpServer)
        .get('/core/suppliers')
        .expect(200);

      expect(response.body).toEqual(mockPaginatedResponse);
      expect(listSpy).toHaveBeenCalled();
    });

    it('should apply query parameters', async () => {
      // Create a spy before making the request
      const listSpy = jest.spyOn(supplierService, 'listSuppliers');
      // Using the httpServer variable that's properly typed
      await request(httpServer)
        .get('/core/suppliers?limit=20&offset=10&name=Test&cnpj=12345678901234')
        .expect(200);

      expect(listSpy).toHaveBeenCalled();
    });
  });

  describe('GET /core/suppliers/:uuid', () => {
    it('should return a supplier by uuid', async () => {
      // Create a spy before making the request
      const findSpy = jest.spyOn(supplierService, 'listSupplierByUuid');
      // Using the httpServer variable that's properly typed
      const response = await request(httpServer)
        .get('/core/suppliers/supplier-uuid')
        .expect(200);

      expect(response.body).toEqual(mockSupplierResponse);
      expect(findSpy).toHaveBeenCalledWith('supplier-uuid');
    });
  });

  describe('DELETE /core/suppliers/:uuid', () => {
    it('should delete a supplier', async () => {
      // Create a spy before making the request
      const deleteSpy = jest.spyOn(supplierService, 'deleteSupplier');
      // Using the httpServer variable that's properly typed
      await request(httpServer)
        .delete('/core/suppliers/supplier-uuid')
        .expect(204);

      expect(deleteSpy).toHaveBeenCalledWith('supplier-uuid');
    });
  });
});
