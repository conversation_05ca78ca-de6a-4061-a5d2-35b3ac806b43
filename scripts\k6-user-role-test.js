import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  vus: 10, // usuários virtuais simultâneos
  duration: '30s', // duração do teste
};

const BASE_URL = 'http://localhost:3000/v1/backoffice';

export default function () {
  // 1. Registro
  const email = `k6user${__VU}_${__ITER}@test.com`;
  const registerPayload = JSON.stringify({
    name: 'Usuário K6',
    email: email,
    password: 'Senha123!',
  });

  let registerRes = http.post(`${BASE_URL}/auth/register`, registerPayload, {
    headers: { 'Content-Type': 'application/json' },
  });

  check(registerRes, {
    'register status 201': (r) => r.status === 201,
  });

  // 2. Login
  const loginPayload = JSON.stringify({
    username: email,
    password: 'Senha123!',
  });

  let loginRes = http.post(`${BASE_URL}/auth/login`, loginPayload, {
    headers: { 'Content-Type': 'application/json' },
  });

  check(loginRes, {
    'login status 201': (r) => r.status === 201,
    'login has token': (r) => r.json('access_token') !== undefined,
  });

  const token = loginRes.json('access_token');

  // 3. /me
  let meRes = http.get(`${BASE_URL}/auth/me`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'accept': 'application/json',
    },
  });

  check(meRes, {
    'me status 200': (r) => r.status === 200,
    'me has email': (r) => r.json('email') === email,
    'me has roles': (r) => Array.isArray(r.json('roles')),
    'me is USER': (r) => r.json('roles').includes('USER'),
  });

  // Adicione aqui outros endpoints permitidos para USER, se desejar

  sleep(1);
} 