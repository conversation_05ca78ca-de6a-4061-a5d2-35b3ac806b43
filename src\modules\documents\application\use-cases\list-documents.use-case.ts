import { Inject, Injectable } from '@nestjs/common';
import { FilterDocumentsDto } from '../../infrastructure/dto/filter-document.dto';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { Document } from '../../domain/entities/document.entity';

@Injectable()
export class ListDocumentsUseCase {
  constructor(
    @Inject('IDocumentRepository')
    private readonly documentRepository: IDocumentRepository,
  ) {}

  async execute(filters: FilterDocumentsDto): Promise<{
    items: Document[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const { entityType, entityUuid, status, limit = 20, offset = 0 } = filters;

    const criteria = {
      ...(entityType && { entityType }),
      ...(entityUuid && { entityUuid }),
      ...(status && { status }),
    };

    const { items, total } = await this.documentRepository.list(
      criteria,
      limit,
      offset,
    );

    return {
      items,
      total,
      limit,
      offset,
    };
  }
}
