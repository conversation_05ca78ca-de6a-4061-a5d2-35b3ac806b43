import { CreateCompanyUseCase } from './create-company.use-case';
import { CompanyRepository } from '../../../ports/repositories/company-repository.interface';
import { Company, CompanyStatus } from '../../../domain/company.entity';

describe('CreateCompanyUseCase', () => {
  let useCase: CreateCompanyUseCase;
  let companyRepository: jest.Mocked<CompanyRepository>;

  beforeEach(() => {
    companyRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findByUuid: jest.fn(),
      findByCnpj: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    useCase = new CreateCompanyUseCase(companyRepository);
  });

  it('should create a new company successfully', async () => {
    const input = {
      name: 'Acme Ltda',
      cnpj: '12345678000195',
      address: {
        street: 'Av. Paulista, 1000',
        city: 'São Paulo',
        zipCode: '01310-100',
        state: 'SP',
      },
      phone: '(11) 98765-4321',
      email: '<EMAIL>',
      status: CompanyStatus.ACTIVE,
      createdBy: 'admin-user',
    };

    const mockCompany = new Company(
      1,
      '550e8400-e29b-41d4-a716-************',
      input.name,
      input.cnpj,
      input.address,
      input.phone,
      input.email,
      input.status,
      input.createdBy,
    );

    const findByCnpjSpy = jest.spyOn(companyRepository, 'findByCnpj');
    const createSpy = jest.spyOn(companyRepository, 'create');

    findByCnpjSpy.mockResolvedValue(null);
    createSpy.mockResolvedValue(mockCompany);

    const result = await useCase.execute(input);

    expect(findByCnpjSpy).toHaveBeenCalledWith(input.cnpj);
    expect(createSpy).toHaveBeenCalled();
    expect(result.company).toEqual(mockCompany.toJSON());
  });

  it('should throw an error if company with CNPJ already exists', async () => {
    const input = {
      name: 'Acme Ltda',
      cnpj: '12345678000195',
      address: {
        street: 'Av. Paulista, 1000',
        city: 'São Paulo',
        zipCode: '01310-100',
        state: 'SP',
      },
      phone: '(11) 98765-4321',
      email: '<EMAIL>',
      status: CompanyStatus.ACTIVE,
      createdBy: 'admin-user',
    };

    const existingCompany = new Company(
      1,
      '550e8400-e29b-41d4-a716-************',
      'Existing Company',
      input.cnpj,
      input.address,
      input.phone,
      input.email,
      input.status,
      'other-user',
    );

    const findByCnpjSpy = jest.spyOn(companyRepository, 'findByCnpj');
    const createSpy = jest.spyOn(companyRepository, 'create');

    findByCnpjSpy.mockResolvedValue(existingCompany);

    await expect(useCase.execute(input)).rejects.toThrow(
      'Já existe uma empresa cadastrada com o CNPJ 12345678000195. Por favor, verifique os dados e tente novamente.',
    );
    expect(findByCnpjSpy).toHaveBeenCalledWith(input.cnpj);
    expect(createSpy).not.toHaveBeenCalled();
  });
});
