import { ApiProperty } from '@nestjs/swagger';

export class PayablesTypeResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the expense type',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  id: string;

  @ApiProperty({
    description: 'Unique code for the expense type',
    example: 'OFFICE',
  })
  code: string;

  @ApiProperty({
    description: 'Description of the expense type',
    example: 'Despesas de Escritório',
  })
  description: string;

  @ApiProperty({
    description: 'UUID of the user who created the expense type',
    example: '11111111-**************-************',
  })
  createdBy: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-05-07T01:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'UUID of the user who last updated the expense type',
    example: '11111111-**************-************',
  })
  updatedBy: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-05-07T01:00:00Z',
  })
  updatedAt: string;
}
