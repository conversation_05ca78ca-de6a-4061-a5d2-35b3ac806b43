import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SESClient,
  SendEmailCommand,
  SendTemplatedEmailCommand,
  SendBulkTemplatedEmailCommand,
} from '@aws-sdk/client-ses';

export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
}

export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: EmailAttachment[];
  replyTo?: string;
}

export interface TemplateOptions {
  templateName: string;
  templateData: Record<string, unknown>;
}

export interface BulkEmailDestination {
  to: string[];
  cc?: string[];
  bcc?: string[];
  templateData: Record<string, unknown>;
}

@Injectable()
export class SESService {
  private readonly sesClient: SESClient;
  private readonly defaultSender: string;

  constructor(private readonly configService: ConfigService) {
    const config = {
      region: this.configService.get<string>('AWS_REGION', 'us-east-2'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID', ''),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
          '',
        ),
      },
    };

    this.sesClient = new SESClient(config);
    this.defaultSender = this.configService.get<string>(
      'SES_DEFAULT_SENDER',
      '<EMAIL>',
    );
  }

  /**
   * Envia um email simples
   * @param options Opções do email
   * @param sender Remetente (se não informado, usa o padrão)
   */
  async sendEmail(options: EmailOptions, sender?: string): Promise<string> {
    try {
      const { to, subject, text, html, cc, bcc, replyTo } = options;

      const command = new SendEmailCommand({
        Source: sender || this.defaultSender,
        Destination: {
          ToAddresses: Array.isArray(to) ? to : [to],
          CcAddresses: cc ? (Array.isArray(cc) ? cc : [cc]) : undefined,
          BccAddresses: bcc ? (Array.isArray(bcc) ? bcc : [bcc]) : undefined,
        },
        Message: {
          Subject: {
            Data: subject,
            Charset: 'UTF-8',
          },
          Body: {
            ...(text && {
              Text: {
                Data: text,
                Charset: 'UTF-8',
              },
            }),
            ...(html && {
              Html: {
                Data: html,
                Charset: 'UTF-8',
              },
            }),
          },
        },
        ReplyToAddresses: replyTo ? [replyTo] : undefined,
      });

      const response = await this.sesClient.send(command);
      console.log(`Email enviado com MessageId: ${response.MessageId}`);

      return response.MessageId || '';
    } catch (error) {
      console.error('Erro ao enviar email via SES:', error);
      throw error;
    }
  }

  /**
   * Envia um email usando um template SES
   * @param to Destinatário(s)
   * @param templateOptions Opções do template
   * @param sender Remetente (se não informado, usa o padrão)
   */
  async sendTemplatedEmail(
    to: string | string[],
    templateOptions: TemplateOptions,
    sender?: string,
  ): Promise<string> {
    try {
      const { templateName, templateData } = templateOptions;

      const command = new SendTemplatedEmailCommand({
        Source: sender || this.defaultSender,
        Destination: {
          ToAddresses: Array.isArray(to) ? to : [to],
        },
        Template: templateName,
        TemplateData: JSON.stringify(templateData),
      });

      const response = await this.sesClient.send(command);
      console.log(
        `Email com template enviado. MessageId: ${response.MessageId}`,
      );

      return response.MessageId || '';
    } catch (error) {
      console.error('Erro ao enviar email com template via SES:', error);
      throw error;
    }
  }

  /**
   * Envia emails em massa usando um template
   * @param destinations Lista de destinatários e dados do template
   * @param templateName Nome do template
   * @param sender Remetente (se não informado, usa o padrão)
   */
  async sendBulkTemplatedEmail(
    destinations: BulkEmailDestination[],
    templateName: string,
    sender?: string,
  ): Promise<string[]> {
    try {
      const formattedDestinations = destinations.map((dest) => ({
        Destination: {
          ToAddresses: dest.to,
          CcAddresses: dest.cc,
          BccAddresses: dest.bcc,
        },
        ReplacementTemplateData: JSON.stringify(dest.templateData),
      }));

      const input = {
        Source: sender || this.defaultSender,
        Template: templateName,
        Destinations: formattedDestinations,
        DefaultTemplateData: JSON.stringify({}),
      };

      const command = new SendBulkTemplatedEmailCommand(input);
      const response = await this.sesClient.send(command);

      if (response.Status && response.Status.length > 0) {
        console.log(`Enviados ${response.Status.length} emails em massa`);
        return response.Status.map((status) => status.MessageId || '');
      }

      return [];
    } catch (error) {
      console.error('Erro ao enviar emails em massa via SES:', error);
      throw error;
    }
  }

  /**
   * Exemplo: Envio de email de boas-vindas
   * @param to Email do destinatário
   * @param name Nome do destinatário
   */
  async sendWelcomeEmail(to: string, name: string): Promise<string> {
    const subject = 'Bem-vindo ao nosso serviço!';

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1>Bem-vindo, ${name}!</h1>
        <p>Estamos muito felizes em ter você como nosso novo usuário.</p>
        <p>Com a nossa plataforma, você poderá:</p>
        <ul>
          <li>Gerenciar suas tarefas</li>
          <li>Colaborar com sua equipe</li>
          <li>Acompanhar seu progresso</li>
        </ul>
        <p>Se precisar de ajuda, não hesite em entrar em contato conosco.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p>Atenciosamente,<br>Equipe de Suporte</p>
        </div>
      </div>
    `;

    return this.sendEmail({
      to,
      subject,
      html,
      text: `Bem-vindo, ${name}! Estamos muito felizes em ter você como nosso novo usuário.`,
    });
  }

  /**
   * Exemplo: Envio de email de redefinição de senha
   * @param to Email do destinatário
   * @param resetToken Token de redefinição
   */
  async sendPasswordResetEmail(
    to: string,
    resetToken: string,
  ): Promise<string> {
    const subject = 'Recuperação de Senha';
    const resetUrl = `${this.configService.get('FRONTEND_URL')}/recuperar-senha/nova-senha?token=${resetToken}`;

    const html = `
        <h1>Recuperação de Senha</h1>
        <p>Você solicitou a recuperação de senha. Clique no link abaixo para redefinir sua senha:</p>
        <p><a href="${resetUrl}">${resetUrl}</a></p>
        <p>Este link é válido por 1 hora.</p>
        <p>Se você não solicitou a recuperação de senha, ignore este e-mail.</p>
      `;

    return this.sendEmail({
      to,
      subject,
      html,
      text: `Recuperação de Senha: Você solicitou a recuperação de senha`,
    });
  }
}
