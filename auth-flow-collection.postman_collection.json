{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Auth Flow Collection", "description": "Collection for testing the complete authentication flow with Keycloak", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Register User", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"access_token\", jsonData.access_token);", "pm.environment.set(\"user_email\", pm.request.body.raw ? JSON.parse(pm.request.body.raw).email : \"\");", "pm.environment.set(\"user_password\", pm.request.body.raw ? JSON.parse(pm.request.body.raw).password : \"\");", "", "pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has access_token\", function () {", "    pm.expect(jsonData).to.have.property('access_token');", "});", "", "pm.test(\"Response has user object\", function () {", "    pm.expect(jsonData).to.have.property('user');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON><PERSON><PERSON>e Postman\",\n    \"email\": \"user-postman-{{$timestamp}}@test.com\",\n    \"password\": \"<PERSON>ha@123Teste\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}, "description": "Register a new user in the system"}, "response": []}, {"name": "2. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"access_token\", jsonData.access_token);", "pm.environment.set(\"refresh_token\", jsonData.refresh_token);", "", "pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has access_token\", function () {", "    pm.expect(jsonData).to.have.property('access_token');", "});", "", "pm.test(\"Response has refresh_token\", function () {", "    pm.expect(jsonData).to.have.property('refresh_token');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{user_email}}\",\n    \"password\": \"{{user_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Authenticate a user and get access and refresh tokens"}, "response": []}, {"name": "3. Get User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Response has user data\", function () {", "    pm.expect(jsonData).to.have.property('email');", "    pm.expect(jsonData).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}, "description": "Get the current user's profile using the access token"}, "response": []}, {"name": "4. <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "// Clear tokens after logout", "pm.environment.unset(\"access_token\");", "pm.environment.unset(\"refresh_token\");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}, "description": "Logout the user and invalidate tokens"}, "response": []}, {"name": "5. <PERSON><PERSON><PERSON> (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401 (Unauthorized)\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Response indicates unauthorized access\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('error');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}, "description": "Try to access a protected resource after logout (should fail with 401)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}]}