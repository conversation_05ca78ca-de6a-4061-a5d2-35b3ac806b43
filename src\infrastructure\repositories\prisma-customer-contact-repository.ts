import { CustomerContactRepositoryPort } from "@/core/ports/repositories/customer-contact-repository.port";
import { Injectable } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CustomerContact } from "@/core/domain/customer/entities/customer-contact.entity";


@Injectable()
export class PrismaCustomerContactRepository implements CustomerContactRepositoryPort {
    constructor(
        private readonly prismaService: PrismaService
    ){}

    async create(contact: Omit<CustomerContact, 'id'>) {
        return await this.prismaService.customerContact.create({
            data: contact
        });
    }

    async findByCustomerId(customerId: number) {
        return await this.prismaService.customerContact.findMany({
            where: { customerId }
        });
    }

    async deleteById(id: string): Promise<void> {
        await this.prismaService.customerContact.delete({
            where: { id }
        })
    }

    async updateById(id: string, contact: Omit<CustomerContact, 'id'>): Promise<CustomerContact> {
        return await this.prismaService.customerContact.update({
            where: { id },
            data: contact
        });
    }
}