import { v4 as uuidv4 } from 'uuid';

export abstract class DomainEvent {
  public readonly eventId: string;
  public readonly occurredAt: Date;
  public readonly correlationId?: string;

  constructor(
    public readonly eventName: string,
    correlationId?: string,
  ) {
    this.eventId = uuidv4();
    this.occurredAt = new Date();
    this.correlationId = correlationId;
  }

  getEventName(): string {
    return this.eventName;
  }

  abstract getData(): object;
}
