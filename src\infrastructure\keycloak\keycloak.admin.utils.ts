import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { RequestUtilsService } from '../utils/request.utils.service';
// import { CircuitBreaker } from '../circuit-breaker/circuit-breaker';
// import { Breakable } from '../utils/circuit.breaker.decorator';

interface AdminTokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}

interface KeycloakUser {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  enabled: boolean;
  createdTimestamp: number;
}

interface KeycloakConfig {
  baseUrl: string;
  realm: string;
  clientId: string;
  clientSecret: string;
  maxRetries: number;
  initialRetryDelayMs: number;
  errorThresholdPercentage: number;
  resetTimeout: number;
  rollingCountMaxErrors: number;
}

interface KeycloakError {
  message: string;
  error: string;
  error_description?: string;
}

/**
 * Utilitário para obter e gerenciar tokens de admin do Keycloak
 * Seguindo o padrão Clean Architecture na camada de infraestrutura
 */
@Injectable()
export class KeycloakAdminUtils {
  private readonly logger = new Logger(KeycloakAdminUtils.name);
  private readonly config: KeycloakConfig;
  private readonly baseUrl: string;
  private readonly realm: string;
  private readonly adminClientId: string;
  private readonly adminUsername: string;
  private readonly adminPassword: string;
  private adminToken: string | null = null;
  private tokenExpiration: Date | null = null;
  private refreshInProgress = false;
  private lastRefreshError: Error | null = null;
  private errorCount = 0;
  private readonly maxErrorsBeforeCircuitBreak = 3;
  private readonly lockoutPeriodMs = 60000; // 1 minuto
  private circuitBreakUntil: Date | null = null;
  // private readonly circuitBreaker: CircuitBreaker;

  constructor(
    private readonly configService: ConfigService,
    private requestUtilsService: RequestUtilsService,
  ) {
    const baseUrl = this.configService.get<string>('KEYCLOAK_BASE_URL');
    const realm = this.configService.get<string>('KEYCLOAK_REALM');
    const clientId = this.configService.get<string>('KEYCLOAK_CLIENT_ID');
    const clientSecret = this.configService.get<string>(
      'KEYCLOAK_CLIENT_SECRET',
    );
    const maxRetries =
      this.configService.get<number>('KEYCLOAK_MAX_RETRIES') ??
      this.configService.get<number>('MAX_RETRIES');
    const initialRetryDelayMs =
      this.configService.get<number>('KEYCLOAK_INITIAL_RETRY_DELAY_MS') ??
      this.configService.get<number>('INITIAL_RETRY_DELAY_MS');
    const errorThresholdPercentage = this.configService.get<number>(
      'KEYCLOAK_ERROR_THRESHOLD_PERCENTAGE',
    );
    const resetTimeout = this.configService.get<number>(
      'KEYCLOAK_RESET_TIMEOUT',
    );
    const rollingCountMaxErrors = this.configService.get<number>(
      'KEYCLOAK_ROLLING_COUNT_MAX_ERRORS',
    );

    if (!baseUrl || !realm || !clientId || !clientSecret) {
      throw new Error('Missing required Keycloak configuration');
    }

    this.config = {
      baseUrl,
      realm,
      clientId,
      clientSecret,
      maxRetries: maxRetries ?? 3,
      initialRetryDelayMs: initialRetryDelayMs ?? 100,
      errorThresholdPercentage: errorThresholdPercentage ?? 50,
      resetTimeout: resetTimeout ?? 60000,
      rollingCountMaxErrors: rollingCountMaxErrors ?? 5,
    };

    this.baseUrl = baseUrl;
    this.realm = realm;
    this.adminClientId = clientId;
    const adminUsername = this.configService.get<string>(
      'KEYCLOAK_ADMIN_USERNAME',
    );
    const adminPassword = this.configService.get<string>(
      'KEYCLOAK_ADMIN_PASSWORD',
    );
    if (!adminUsername || !adminPassword) {
      throw new Error('Missing required Keycloak admin username or password');
    }
    this.adminUsername = adminUsername;
    this.adminPassword = adminPassword;

    // this.circuitBreaker = new CircuitBreaker({
    //   maxRetries: this.config.maxRetries,
    //   initialRetryDelayMs: this.config.initialRetryDelayMs,
    //   errorThresholdPercentage: this.config.errorThresholdPercentage,
    //   resetTimeout: this.config.resetTimeout,
    //   rollingCountMaxErrors: this.config.rollingCountMaxErrors,
    // });
  }

  /**
   * Obtém um token de administrador do Keycloak usando o fluxo client_credentials
   * Reutiliza o token em cache se ainda for válido
   */
  async getAdminToken(): Promise<string> {
    // Verifica se o circuito está aberto (muitos erros recentes)
    if (this.isCircuitBroken()) {
      throw new Error(
        'Keycloak admin API circuit is broken due to too many errors',
      );
    }

    // Reutiliza o token se ele ainda for válido (considerando um buffer de segurança de 30 segundos)
    if (
      this.adminToken &&
      this.tokenExpiration &&
      this.tokenExpiration > new Date(Date.now() + 30000)
    ) {
      return this.adminToken;
    }

    // Previne múltiplas requisições paralelas para refresh
    if (this.refreshInProgress) {
      // Se houver uma tentativa de refresh já em andamento, aguarda 100ms e tenta novamente
      await new Promise((resolve) => setTimeout(resolve, 100));
      return this.getAdminToken();
    }

    try {
      this.refreshInProgress = true;
      const response =
        await this.requestUtilsService.executeWithRetry<AdminTokenResponse>(
          () => {
            const params = new URLSearchParams();
            params.append('grant_type', 'password');
            params.append('client_id', this.adminClientId);
            params.append('username', this.adminUsername);
            params.append('password', this.adminPassword);

            if (this.config.clientSecret) {
              params.append('client_secret', this.config.clientSecret);
            }

            return axios.post(
              `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`,
              params.toString(),
              {
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
              },
            );
          },
          (error) => {
            let message = '';
            if (error instanceof Error) {
              message = error.message;
            } else {
              message = String(error);
            }
            throw new Error(
              `Falha ao obter token admin do Keycloak: ${message}`,
            );
          },
        );

      this.adminToken = response.access_token;
      this.tokenExpiration = new Date(Date.now() + response.expires_in * 1000);
      this.errorCount = 0;
      this.lastRefreshError = null;

      return this.adminToken;
    } catch (error) {
      this.handleRefreshError(error);
      throw error;
    } finally {
      this.refreshInProgress = false;
    }
  }

  /**
   * Verifica se o circuito está aberto devido a muitos erros
   */
  private isCircuitBroken(): boolean {
    if (this.circuitBreakUntil && this.circuitBreakUntil > new Date()) {
      console.warn(
        `Keycloak admin API circuit broken until ${this.circuitBreakUntil.toISOString()}`,
      );
      return true;
    }

    if (this.circuitBreakUntil && this.circuitBreakUntil <= new Date()) {
      // Reseta o estado do circuito
      this.errorCount = 0;
      this.circuitBreakUntil = null;
      this.lastRefreshError = null;
      console.info('Keycloak admin API circuit reset after cooling period');
    }

    return false;
  }

  /**
   * Solicita um novo token de administrador do Keycloak usando password grant type
   * Ignora cache e sempre busca um token novo
   */
  async refreshAdminToken(): Promise<string> {
    this.refreshInProgress = true;

    try {
      const params = new URLSearchParams();
      // Usar o fluxo password grant ao invés de client_credentials
      params.append('grant_type', 'password');
      // Usar o admin-cli que tem permissões para gerenciar usuários
      params.append('client_id', this.adminClientId);

      // Adicionar client_secret já que o cliente não é mais public
      if (this.config.clientSecret) {
        params.append('client_secret', this.config.clientSecret);
      }

      params.append('username', this.adminUsername);
      params.append('password', this.adminPassword);

      // Configurações de timeout baseadas no .env
      // const timeout = this.configService.get<number>('KEYCLOAK_TIMEOUT', 10000);

      const tokenUrl = `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`;
      console.log(
        `Obtendo token admin do Keycloak via password grant: ${tokenUrl}`,
      );

      const response =
        await this.requestUtilsService.executeWithRetry<AdminTokenResponse>(
          () =>
            axios.post(tokenUrl, params.toString(), {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Accept: 'application/json',
              },
              // timeout,
            }),
          (error) => {
            this.handleRefreshError(error);
            throw new Error(
              `Falha ao obter token admin do Keycloak: ${String(error)}`,
            );
          },
        );

      // Armazena o token e calcula a expiração
      this.adminToken = response.access_token;
      this.tokenExpiration = new Date(Date.now() + response.expires_in * 1000);

      // Reseta contadores de erro ao ter sucesso
      this.errorCount = 0;
      this.lastRefreshError = null;

      console.debug(
        `Token admin obtido com sucesso, válido até ${this.tokenExpiration.toISOString()}`,
      );

      return this.adminToken;
    } finally {
      this.refreshInProgress = false;
    }
  }

  /**
   * Gerencia erros durante a obtenção de token, incluindo circuit breaking
   */
  private handleRefreshError(error: unknown): void {
    this.errorCount++;
    this.lastRefreshError =
      error instanceof Error ? error : new Error(String(error));

    console.error(
      `Erro ao obter token admin (${this.errorCount}/${this.maxErrorsBeforeCircuitBreak}): ${String(error)}`,
    );

    // Se atingir o limite de erros, abre o circuito
    if (this.errorCount >= this.maxErrorsBeforeCircuitBreak) {
      this.circuitBreakUntil = new Date(Date.now() + this.lockoutPeriodMs);
      console.warn(
        `Keycloak admin API circuit broken until ${this.circuitBreakUntil.toISOString()}`,
      );
    }
  }

  /**
   * Cria cabeçalhos de autorização para requisições ao Keycloak Admin API
   */
  async getAdminAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.getAdminToken();
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Invalida o token atual forçando uma nova obtenção na próxima chamada
   */
  invalidateToken(): void {
    this.adminToken = null;
    this.tokenExpiration = null;
  }

  /**
   * Lista os usuários do Keycloak
   * @param search Termo de busca opcional para filtrar usuários
   * @returns Lista de usuários do Keycloak
   */
  async listUsers(search?: string): Promise<KeycloakUser[]> {
    try {
      const headers = await this.getAdminAuthHeaders();
      const baseUrl = this.configService.get<string>('KEYCLOAK_BASE_URL');
      const realm = this.configService.get<string>('KEYCLOAK_REALM');

      let url = `${baseUrl}/admin/realms/${realm}/users`;
      if (search) {
        url += `?search=${encodeURIComponent(search)}`;
      }

      console.log(`Listando usuários do Keycloak: ${url}`);

      const response = await axios.get<KeycloakUser[]>(url, { headers });
      return response.data;
    } catch (error) {
      console.error('Erro ao listar usuários do Keycloak:', error);
      throw new Error(`Falha ao listar usuários do Keycloak: ${String(error)}`);
    }
  }

  /**
   * Busca um usuário específico no Keycloak por email
   * @param email Email do usuário a ser buscado
   * @returns Usuário encontrado ou null se não existir
   */
  async findUserByEmail(email: string): Promise<KeycloakUser | null> {
    try {
      const users = await this.listUsers(email);
      return users.find((user) => user.email === email) || null;
    } catch (error) {
      console.error(`Erro ao buscar usuário por email (${email}):`, error);
      return null;
    }
  }

  /**
   * Cria uma role no Keycloak se ela não existir
   * @param roleName Nome da role a ser criada
   * @param description Descrição opcional da role
   * @returns True se a role foi criada, false se já existia
   */
  async createRoleIfNotExists(
    roleName: string,
    description?: string,
  ): Promise<boolean> {
    try {
      const headers = await this.getAdminAuthHeaders();

      // Verificar se a role já existe
      const rolesUrl = `${this.baseUrl}/admin/realms/${this.realm}/roles`;
      const rolesResponse = await axios.get(rolesUrl, { headers });

      const existingRoles = rolesResponse.data as Array<{ name: string }>;
      if (existingRoles.some((role) => role.name === roleName)) {
        console.log(`Role '${roleName}' já existe no Keycloak.`);
        return false;
      }

      // Criar a role
      const roleData = {
        name: roleName,
        description: description || `Role ${roleName}`,
      };

      await axios.post(rolesUrl, roleData, { headers });
      console.log(`Role '${roleName}' criada com sucesso no Keycloak.`);
      return true;
    } catch (error) {
      console.error(`Erro ao criar role '${roleName}' no Keycloak:`, error);
      throw new Error(
        `Falha ao criar role no Keycloak: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  async createUser(
    adminToken: string,
    userData: Record<string, unknown>,
  ): Promise<string> {
    try {
      const response = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/users`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${adminToken}`,
          },
          body: JSON.stringify(userData),
        },
      );

      if (!response.ok) {
        throw new Error(
          'Location header not returned by Keycloak when creating user',
        );
      }

      const location = response.headers.get('location');
      if (!location) {
        throw new Error(
          'Location header not returned by Keycloak when creating user',
        );
      }
      const userId = location.split('/').pop();
      if (!userId) {
        throw new Error('Failed to extract user ID from location header');
      }
      return userId;
    } catch (error) {
      this.logger.error(
        `Error creating user: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async updateUser(
    adminToken: string,
    userId: string,
    userData: Record<string, unknown>,
  ): Promise<void> {
    try {
      const response = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/users/${userId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${adminToken}`,
          },
          body: JSON.stringify(userData),
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to update user: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error(
        `Error updating user: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async deleteUser(adminToken: string, userId: string): Promise<void> {
    try {
      const response = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/users/${userId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${adminToken}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to delete user: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error(
        `Error deleting user: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Garante que a role existe no Keycloak, criando se necessário
   */
  async ensureRoleExists(roleName: string): Promise<void> {
    const headers = await this.getAdminAuthHeaders();
    const rolesUrl = `${this.baseUrl}/admin/realms/${this.realm}/roles`;
    // Verifica se a role já existe
    const rolesResponse = await fetch(rolesUrl, { headers });
    if (!rolesResponse.ok) {
      throw new Error(`Failed to list roles: ${rolesResponse.statusText}`);
    }
    const existingRoles = (await rolesResponse.json()) as Array<{
      name: string;
    }>;
    if (existingRoles.some((role) => role.name === roleName)) {
      return;
    }
    // Cria a role
    const createResponse = await fetch(rolesUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: roleName,
        description: `Auto-created role ${roleName}`,
      }),
    });
    if (!createResponse.ok) {
      throw new Error(
        `Failed to create role '${roleName}': ${createResponse.statusText}`,
      );
    }
  }

  async assignRole(
    adminToken: string,
    userId: string,
    role: string,
  ): Promise<void> {
    // Garante que a role existe antes de atribuir
    await this.ensureRoleExists(role);
    try {
      // First, get the role ID
      const roleResponse = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/roles/${role}`,
        {
          headers: {
            Authorization: `Bearer ${adminToken}`,
          },
        },
      );

      if (!roleResponse.ok) {
        throw new Error(`Failed to get role: ${roleResponse.statusText}`);
      }

      const roleData = (await roleResponse.json()) as Record<string, unknown>;

      // Then assign the role to the user
      const response = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/users/${userId}/role-mappings/realm`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${adminToken}`,
          },
          body: JSON.stringify([roleData]),
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to assign role: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error(
        `Error assigning role: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async removeRole(
    adminToken: string,
    userId: string,
    role: string,
  ): Promise<void> {
    try {
      // First, get the role ID
      const roleResponse = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/roles/${role}`,
        {
          headers: {
            Authorization: `Bearer ${adminToken}`,
          },
        },
      );

      if (!roleResponse.ok) {
        throw new Error(`Failed to get role: ${roleResponse.statusText}`);
      }

      const roleData = (await roleResponse.json()) as Record<string, unknown>;

      // Then remove the role from the user
      const response = await fetch(
        `${this.config.baseUrl}/admin/realms/${this.config.realm}/users/${userId}/role-mappings/realm`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${adminToken}`,
          },
          body: JSON.stringify([roleData]),
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to remove role: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error(
        `Error removing role: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Cria todas as roles do domínio (conforme enum Role do schema.prisma)
   */
  async createAllDomainRoles(): Promise<void> {
    const roles = [
      'ADMIN',
      'USER',
      'FINANCE_ADMIN',
      'FINANCE_USER',
      'DOCUMENT_ARCHIVER',
      'DOCUMENT_UPLOADER',
      'DOCUMENT_VIEWER',
    ];
    for (const role of roles) {
      await this.ensureRoleExists(role);
    }
  }

  private handleError(error: unknown): never {
    if (error instanceof Error) {
      this.logger.error(`Keycloak error: ${error.message}`);
      throw error;
    }
    const keycloakError = error as KeycloakError;
    this.logger.error(`Keycloak error: ${keycloakError.message}`);
    throw new Error(keycloakError.message);
  }
}
