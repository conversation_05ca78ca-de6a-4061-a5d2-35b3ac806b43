import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FileReaderService } from '../services/file-reader.service';
import { FileReaderOptions } from '../interfaces/file-reader.interface';

interface CustomerImportDto {
  validateEmails?: boolean;
  skipDuplicates?: boolean;
  requiredColumns?: string[];
}

interface ImportResult {
  totalProcessed: number;
  successfulImports: number;
  errors: Array<{
    row: number;
    message: string;
    data?: any;
  }>;
  duplicatesSkipped: number;
  preview: any[];
}

@ApiTags('Customer Import')
@Controller('customers/import')
export class CustomersImportController {
  constructor(private readonly fileReaderService: FileReaderService) {}

  @Post('csv')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import customers from CSV file' })
  @ApiResponse({ status: 200, description: 'Customers imported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file or data' })
  async importCustomersFromCsv(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: CustomerImportDto = {},
  ): Promise<ImportResult> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Define required columns for customer import
    const defaultRequiredColumns = ['name', 'email'];
    const requiredColumns = options.requiredColumns || defaultRequiredColumns;

    const fileReaderOptions: FileReaderOptions = {
      requiredColumns,
      trimValues: true,
      skipEmptyLines: true,
      delimiter: ',',
    };

    const parseResult = await this.fileReaderService.readFile(file, fileReaderOptions);

    return this.processCustomerData(parseResult.data, options);
  }

  @Post('excel')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import customers from Excel file' })
  @ApiResponse({ status: 200, description: 'Customers imported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file or data' })
  async importCustomersFromExcel(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: CustomerImportDto = {},
  ): Promise<ImportResult> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Define required columns for customer import
    const defaultRequiredColumns = ['name', 'email'];
    const requiredColumns = options.requiredColumns || defaultRequiredColumns;

    const fileReaderOptions: FileReaderOptions = {
      requiredColumns,
      trimValues: true,
      skipEmptyLines: true,
    };

    const parseResult = await this.fileReaderService.readFile(file, fileReaderOptions);

    return this.processCustomerData(parseResult.data, options);
  }

  @Post('validate')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Validate customer import file without importing' })
  @ApiResponse({ status: 200, description: 'File validation completed' })
  async validateCustomerFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: CustomerImportDto = {},
  ): Promise<{
    isValid: boolean;
    fileInfo: any;
    columnValidation: any;
    dataPreview: any[];
    errors: string[];
  }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const defaultRequiredColumns = ['name', 'email'];
    const requiredColumns = options.requiredColumns || defaultRequiredColumns;

    // Get file info first
    const fileInfo = await this.fileReaderService.getFileInfo(file);
    
    // Check column validation
    const missingColumns = requiredColumns.filter(
      required => !fileInfo.columns?.some(col => 
        col.toLowerCase().trim() === required.toLowerCase().trim()
      )
    );

    const columnValidation = {
      hasRequiredColumns: missingColumns.length === 0,
      missingColumns,
      availableColumns: fileInfo.columns || [],
    };

    let dataPreview: any[] = [];
    let errors: string[] = [];

    if (columnValidation.hasRequiredColumns) {
      try {
        // Parse first few rows for preview
        const parseResult = await this.fileReaderService.readFile(file, {
          requiredColumns,
          trimValues: true,
          skipEmptyLines: true,
        });

        dataPreview = parseResult.data.slice(0, 5); // First 5 rows
        
        // Basic validation on preview data
        dataPreview.forEach((row, index) => {
          if (options.validateEmails && row.email && !this.isValidEmail(row.email)) {
            errors.push(`Row ${index + 2}: Invalid email format - ${row.email}`);
          }
          
          if (!row.name || row.name.trim().length === 0) {
            errors.push(`Row ${index + 2}: Name is required`);
          }
        });
      } catch (error) {
        errors.push(`File parsing error: ${error.message}`);
      }
    }

    return {
      isValid: columnValidation.hasRequiredColumns && errors.length === 0,
      fileInfo,
      columnValidation,
      dataPreview,
      errors,
    };
  }

  private async processCustomerData(
    data: Record<string, any>[],
    options: CustomerImportDto,
  ): Promise<ImportResult> {
    const result: ImportResult = {
      totalProcessed: data.length,
      successfulImports: 0,
      errors: [],
      duplicatesSkipped: 0,
      preview: [],
    };

    const processedEmails = new Set<string>();

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // +2 because of header and 0-based index

      try {
        // Normalize customer data
        const customer = this.normalizeCustomerData(row);

        // Validate email format if required
        if (options.validateEmails && !this.isValidEmail(customer.email)) {
          result.errors.push({
            row: rowNumber,
            message: `Invalid email format: ${customer.email}`,
            data: row,
          });
          continue;
        }

        // Check for duplicates within the file
        if (options.skipDuplicates && processedEmails.has(customer.email.toLowerCase())) {
          result.duplicatesSkipped++;
          continue;
        }

        processedEmails.add(customer.email.toLowerCase());

        // Here you would typically save to database
        // await this.customersService.create(customer);

        result.successfulImports++;
        
        // Add to preview (first 5 successful records)
        if (result.preview.length < 5) {
          result.preview.push(customer);
        }

      } catch (error) {
        result.errors.push({
          row: rowNumber,
          message: error.message,
          data: row,
        });
      }
    }

    return result;
  }

  private normalizeCustomerData(row: Record<string, any>): any {
    // Handle different column name variations
    const name = row.name || row.Name || row.NAME || row['Nome'] || row['NOME'];
    const email = row.email || row.Email || row.EMAIL || row['E-mail'] || row['EMAIL'];
    const phone = row.phone || row.Phone || row.PHONE || row['Telefone'] || row['TELEFONE'];
    const company = row.company || row.Company || row.COMPANY || row['Empresa'] || row['EMPRESA'];

    if (!name || name.trim().length === 0) {
      throw new Error('Name is required');
    }

    if (!email || email.trim().length === 0) {
      throw new Error('Email is required');
    }

    return {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: phone ? phone.trim().replace(/\D/g, '') : null, // Remove non-digits
      company: company ? company.trim() : null,
      // Add other fields as needed
      createdAt: new Date(),
      source: 'file_import',
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
