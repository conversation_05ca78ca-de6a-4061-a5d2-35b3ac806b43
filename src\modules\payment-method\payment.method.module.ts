import { Module } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { PaymentMethodController } from './payment.method.controller';
import { CreatePaymentMethodUseCase } from '@/core/application/use-cases/payment-method/create-payment-method-use-case';
import { PrismaPaymentMethodRepository } from '@/infrastructure/repositories/prisma-payment-method.repository';
import { ListPaymentMethodUseCase } from '@/core/application/use-cases/payment-method/list-payment-methods-use-case';
import { UpdatePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/update-payment-method-use-case';
import { DeletePaymentMethodUseCase } from '../../core/application/use-cases/payment-method/delete-payment-methods-use-case';

@Module({
  controllers: [PaymentMethodController],
  providers: [
    CreatePaymentMethodUseCase,
    ListPaymentMethodUseCase,
    UpdatePaymentMethodUseCase,
    DeletePaymentMethodUseCase,
    {
      provide: 'PaymentMethodRepository',
      useClass: PrismaPaymentMethodRepository,
    },
    PrismaService,
  ],
})
export class PaymentMethodModule {}
