import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { DocumentVersion } from '../../domain/entities/document-version.entity';

@Injectable()
export class ListDocumentVersionsUseCase {
  constructor(private readonly prisma: PrismaService) {}

  async execute(documentId: string): Promise<DocumentVersion[]> {
    const document = await this.prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Documento não encontrado');
    }

    const versions = await this.prisma.documentVersion.findMany({
      where: { documentId },
      orderBy: { uploadedAt: 'asc' },
    });

    return versions as unknown as DocumentVersion[];
  }
}
