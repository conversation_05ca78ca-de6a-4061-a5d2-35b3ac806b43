import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, MaxLength, MinLength } from 'class-validator';

export class CreatePayablesTypeDto {
  @ApiProperty({
    description: 'Unique code for the expense type',
    example: 'OFFICE',
    minLength: 1,
    maxLength: 50,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  code: string;

  @ApiProperty({
    description: 'Description of the expense type',
    example: 'Despesas de Escritório',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  description: string;

  @ApiProperty({
    description: 'UUID of the user creating the expense type',
    example: '11111111-**************-************',
  })
  @IsUUID()
  createdBy: string;

  @ApiProperty({
    description: 'UUID of the user updating the expense type',
    example: '11111111-**************-************',
  })
  @IsUUID()
  updatedBy: string;
}
