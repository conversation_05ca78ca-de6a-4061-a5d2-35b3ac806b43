import { Injectable, Inject } from '@nestjs/common';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';

@Injectable()
export class DeleteCustomerContractUseCase {
  constructor(
    @Inject('ICustomerContractRepository')
    private readonly repository: ICustomerContractRepository,
  ) {}

  async execute(customerUuid: string, contractUuid: string): Promise<void> {
    await this.repository.delete(customerUuid, contractUuid);
  }
} 