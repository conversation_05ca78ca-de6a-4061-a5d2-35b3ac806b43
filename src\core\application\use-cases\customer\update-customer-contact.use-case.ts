import { CustomerContact } from "@/core/domain/customer/entities/customer-contact.entity";
import { CustomerContactRepositoryPort } from "@/core/ports/repositories/customer-contact-repository.port";
import { Injectable, Inject } from '@nestjs/common';

@Injectable()
export class UpdateCustomerContactUseCase {
  constructor(
    @Inject('CustomerContactRepository')
    private readonly customerContactRepository: CustomerContactRepositoryPort,
  ) {}

  async execute(id: string, contactData: Partial<Omit<CustomerContact, 'createdAt' | 'updatedAt' | 'id' | 'customerId'>>): Promise<CustomerContact> {
    const contact = await this.customerContactRepository.updateById(id, {
      ...contactData,
    })

    return contact;
  }
}