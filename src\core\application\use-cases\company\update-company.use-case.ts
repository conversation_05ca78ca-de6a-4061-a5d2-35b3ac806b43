import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { CompanyRepository } from '../../../ports/repositories/company-repository.interface';
import { Company, CompanyStatus } from '../../../domain/company.entity';
import { COMPANY_REPOSITORY } from '../../../ports/repositories/tokens';

export interface UpdateCompanyInput {
  uuid: string;
  razaoSocial?: string;
  cnpj?: string;
  address?: {
    street: string;
    city: string;
    zipCode: string;
    state: string;
  };
  phone?: string;
  email?: string;
  status?: CompanyStatus;
  updatedBy: string;
}

@Injectable()
export class UpdateCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(input: UpdateCompanyInput): Promise<{ company: Company }> {
    const existingCompany = await this.companyRepository.findByUuid(input.uuid);

    if (!existingCompany) {
      throw new NotFoundException('Empresa não encontrada');
    }

    if (input.cnpj && input.cnpj !== existingCompany.cnpj) {
      const companyWithSameCnpj = await this.companyRepository.findByCnpj(
        input.cnpj,
      );
      if (companyWithSameCnpj) {
        throw new ConflictException(
          'Já existe uma empresa cadastrada com este CNPJ',
        );
      }
    }

    if (input.razaoSocial) {
      existingCompany.updateRazaoSocial(input.razaoSocial, input.updatedBy);
    }

    if (input.address) {
      existingCompany.updateAddress(input.address, input.updatedBy);
    }

    if (input.phone) {
      existingCompany.updatePhone(input.phone, input.updatedBy);
    }

    if (input.email) {
      existingCompany.updateEmail(input.email, input.updatedBy);
    }

    if (input.status) {
      existingCompany.updateStatus(input.status, input.updatedBy);
    }

    const updatedCompany = await this.companyRepository.update(existingCompany);

    return { company: updatedCompany };
  }
}
