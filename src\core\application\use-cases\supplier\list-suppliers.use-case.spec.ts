import { Test, TestingModule } from '@nestjs/testing';
import { ListSuppliersUseCase } from './list-suppliers.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';

describe('ListSuppliersUseCase', () => {
  let useCase: ListSuppliersUseCase;
  let mockSupplierRepository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    '123e4567-e89b-12d3-a456-426614174000',
    'Test Supplier',
    '12345678901234',
    'Test Trade Name',
    new Address(
      'Test Street',
      '123', // number
      'Apt 1', // complement
      'Centro', // neighborhood
      'Test City',
      '12345-678',
      'Test State',
    ),
    '<EMAIL>', // email
    'CORE', // classification (SupplierClassification)
    SupplierType.GAME,
    SupplierStatus.ACTIVE,
    'user123', // userId
    'creatorUser', // createdBy
    new Date('2024-01-01T00:00:00Z'), // createdAt
    new Date('2024-01-01T00:00:00Z'), // updatedAt
    'creatorUser', // updatedBy
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      findWithPagination: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findByUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ListSuppliersUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<ListSuppliersUseCase>(ListSuppliersUseCase);
    mockSupplierRepository =
      module.get<jest.Mocked<SupplierRepositoryPort>>(SUPPLIER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should return paginated suppliers without filters', async () => {
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockSupplierRepository,
        'findWithPagination',
      );

      mockSupplierRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      });
    });

    it('should return paginated suppliers with CNPJ filter', async () => {
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockSupplierRepository,
        'findWithPagination',
      );

      mockSupplierRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        cnpj: '12345678901234',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        cnpj: '12345678901234',
      });
    });

    it('should return paginated suppliers with name filter', async () => {
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockSupplierRepository,
        'findWithPagination',
      );

      mockSupplierRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        name: 'Test',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        name: 'Test',
      });
    });

    it('should return paginated suppliers with both filters', async () => {
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const findWithPagination = jest.spyOn(
        mockSupplierRepository,
        'findWithPagination',
      );

      mockSupplierRepository.findWithPagination.mockResolvedValue(mockResult);

      const result = await useCase.execute({
        limit: 10,
        offset: 0,
        cnpj: '12345678901234',
        name: 'Test',
      });

      expect(result).toEqual(mockResult);
      expect(findWithPagination).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        cnpj: '12345678901234',
        name: 'Test',
      });
    });
  });
});
