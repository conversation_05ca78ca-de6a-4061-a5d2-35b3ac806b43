import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CompanyRepositoryPort } from '../../core/ports/repositories/company-repository.port';
import { Company } from '../../core/domain/entities/company.entity';
import { CompanyEntity } from '../persistence/entities/company.entity';
import { CompanyStatus } from '@prisma/client';
import { CompanyStatus as DomainCompanyStatus } from '../../core/domain/company.entity';

@Injectable()
export class PrismaCompanyRepository implements CompanyRepositoryPort {
  constructor(private readonly prisma: PrismaService) {}

  async create(company: Company): Promise<Company> {
    const data = {
      uuid: company.uuid,
      cnpj: company.cnpj,
      razaoSocial: company.razaoSocial,
      address: company.address as object,
      phone: company.phone,
      email: company.email,
      status: company.status as CompanyStatus,
      createdBy: company.createdBy,
      updatedBy: company.updatedBy,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
    };

    const created = await this.prisma.company.create({ data });
    return this.toDomain({
      ...created,
      address: toCompanyAddress(created.address),
      status: toDomainCompanyStatus(created.status),
    });
  }

  async findById(id: number): Promise<Company | null> {
    const company = await this.prisma.company.findUnique({
      where: { id },
    });
    if (!company || company.deletedAt) return null;
    return this.toDomain({
      ...company,
      address: toCompanyAddress(company.address),
      status: toDomainCompanyStatus(company.status),
    });
  }

  async findByUuid(uuid: string): Promise<Company | null> {
    const company = await this.prisma.company.findUnique({
      where: { uuid },
    });
    if (!company || company.deletedAt) return null;
    return this.toDomain({
      ...company,
      address: toCompanyAddress(company.address),
      status: toDomainCompanyStatus(company.status),
    });
  }

  async findByCnpj(cnpj: string): Promise<Company | null> {
    const company = await this.prisma.company.findFirst({
      where: { cnpj },
    });
    if (!company || company.deletedAt) return null;
    return this.toDomain({
      ...company,
      address: toCompanyAddress(company.address),
      status: toDomainCompanyStatus(company.status),
    });
  }

  async findAll(): Promise<Company[]> {
    const companies = await this.prisma.company.findMany({
      where: { deletedAt: null },
    });
    return companies.map((company) =>
      this.toDomain({
        ...company,
        address: toCompanyAddress(company.address),
        status: toDomainCompanyStatus(company.status),
      })
    );
  }

  async update(company: Company): Promise<Company> {
    const data = {
      cnpj: company.cnpj,
      razaoSocial: company.razaoSocial,
      address: company.address as object,
      phone: company.phone,
      email: company.email,
      status: company.status as CompanyStatus,
      updatedBy: company.updatedBy,
      updatedAt: company.updatedAt,
    };

    const updated = await this.prisma.company.update({
      where: { uuid: company.uuid },
      data,
    });

    return this.toDomain({
      ...updated,
      address: toCompanyAddress(updated.address),
      status: toDomainCompanyStatus(updated.status),
    });
  }

  async delete(id: number): Promise<void> {
    await this.prisma.company.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  private toDomain(prismaCompany: CompanyEntity): Company {
    return new Company(
      prismaCompany.id,
      prismaCompany.uuid,
      prismaCompany.cnpj,
      prismaCompany.razaoSocial,
      (prismaCompany.address && typeof prismaCompany.address === 'object' && prismaCompany.address !== null
        ? prismaCompany.address
        : { street: '', city: '', zipCode: '', state: '' }) as Company['address'],
      prismaCompany.phone,
      prismaCompany.email,
      prismaCompany.status as string,
      prismaCompany.createdBy,
      prismaCompany.updatedBy,
      prismaCompany.createdAt,
      prismaCompany.updatedAt,
    );
  }
}

function toCompanyAddress(address: any): { street: string; city: string; zipCode: string; state: string } {
  if (
    address &&
    typeof address === 'object' &&
    'street' in address &&
    'city' in address &&
    'zipCode' in address &&
    'state' in address
  ) {
    return address as { street: string; city: string; zipCode: string; state: string };
  }
  return { street: '', city: '', zipCode: '', state: '' };
}

function toDomainCompanyStatus(status: any): DomainCompanyStatus {
  if (status === 'ACTIVE') return DomainCompanyStatus.ACTIVE;
  if (status === 'INACTIVE') return DomainCompanyStatus.INACTIVE;
  return DomainCompanyStatus.ACTIVE;
}
