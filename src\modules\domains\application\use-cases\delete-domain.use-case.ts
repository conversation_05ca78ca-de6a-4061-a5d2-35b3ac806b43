import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IDeleteDomainUseCase } from '../../domain/use-cases/delete-domain.use-case.interface';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';

@Injectable()
export class DeleteDomainUseCase implements IDeleteDomainUseCase {
  constructor(
    @Inject('IDomainRepository')
    private readonly domainRepository: IDomainRepository,
  ) {}

  async execute(uuid: string): Promise<void> {
    const domain = await this.domainRepository.findByUuid(uuid);
    if (!domain) {
      throw new NotFoundException(`Domain with UUID ${uuid} not found`);
    }
    await this.domainRepository.delete(uuid);
  }
} 