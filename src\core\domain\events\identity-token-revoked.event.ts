import { DomainEvent } from './domain-event';
import { v4 as uuidv4 } from 'uuid';

export class IdentityTokenRevokedEvent implements DomainEvent {
  public readonly eventId: string;
  public readonly eventName: string;
  public readonly occurredAt: Date;

  constructor(
    public readonly userId: string,
    public readonly tokenId: string,
    public readonly metadata: Record<string, unknown>,
  ) {
    this.eventId = uuidv4();
    this.eventName = 'identity.token.revoked';
    this.occurredAt = new Date();
  }

  serialize(): Record<string, unknown> {
    return {
      userId: this.userId,
      tokenId: this.tokenId,
      timestamp: this.occurredAt.toISOString(),
      ...this.metadata,
    };
  }

  getData(): Record<string, unknown> {
    return this.serialize();
  }
}
