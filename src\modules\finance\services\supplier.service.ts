import { Injectable, Inject, NotFoundException } from '@nestjs/common'; // Adicionar NotFoundException
import { CreateSupplierUseCase } from '@/core/application/use-cases/supplier/create-supplier.use-case';
import { CreateSupplierDto } from '../dto/create-supplier.dto';
import { SupplierResponseDto } from '../dto/supplier-response.dto';
import { Supplier } from '@/core/domain/supplier/entities/supplier.entity';
import { ListSupplierByUuidUseCase } from '@/core/application/use-cases/supplier/list-supplier-by-uuid.use-case';
import { ListSuppliersUseCase } from '@/core/application/use-cases/supplier/list-suppliers.use-case';
import { ListSuppliersDto } from '../dto/list-suppliers.dto';
import { PaginatedSuppliersResponseDto } from '../dto/paginated-suppliers-response.dto';

import { DeleteSupplierUseCase } from '@/core/application/use-cases/supplier/delete-supplier.use-case';

import { UpdateSupplierUseCase } from '@/core/application/use-cases/supplier/update-supplier.use-case';
import { UpdateSupplierDto } from '../dto/update-supplier.dto';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';
import { CreateDocumentUseCase } from '../../documents/application/use-cases/create-document.use-case';
import { CreateDocumentDto } from '../../documents/infrastructure/dto/create-document.dto';
import { EntityType } from '../../documents/domain/enums/entity-type.enum';
import { Document } from '../../documents/domain/entities/document.entity';
import { ListDocumentsUseCase } from '../../documents/application/use-cases/list-documents.use-case';
import { DownloadDocumentUseCase } from '../../documents/application/use-cases/download-document.use-case';
import { ListSupplierContactsUseCase } from '@/core/application/use-cases/supplier/list-supplier-contacts.use-case';
import { CreateSupplierContactUseCase } from '@/core/application/use-cases/supplier/create-supplier-contact.use-case';

import { ContactResponseDto } from '../dto/supplier-response.dto';
import { GetSupplierByUserIdUsecase } from '@/core/application/use-cases/supplier/get-supplier-by-user-id.use-case';
import { CreateContractUseCase } from '@/modules/documents/application/use-cases/create-contract.use-case';
import { Contract } from '@/modules/documents/domain/entities/contract.entity';
import { CreateContractsDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { ListContractUseCase } from '@/modules/documents/application/use-cases/list-contract.use-case';
import { DownloadContractUseCase } from '@/modules/documents/application/use-cases/download-contract.use-case';
import { PrismaService } from '../../../infrastructure/prisma/prisma.service'; // Corrigir o caminho para PrismaService
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';
import { DeleteContractUseCase } from '@/modules/documents/application/use-cases/delete-contract.use-case';
import { IContractRepository } from '@/modules/documents/domain/repositories/contract.repository.interface';
import { UpdateContractUseCase } from '@/modules/documents/application/use-cases/update-contract.use-case';
import { SupplierContractSignPatchDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { EntityType as PrismaEntityType, ContractType as PrismaContractType, ContractStatus } from '@prisma/client';
import { UpdateSupplierContactUseCase } from '@/core/application/use-cases/supplier/update-supplier-contact.usecase';
import { DeleteSupplierContactUseCase } from '@/core/application/use-cases/supplier/delete-supplier-contact.usecase';

@Injectable()
export class SupplierService {
  constructor(
    private readonly createSupplierUseCase: CreateSupplierUseCase,
    private readonly listSupplierByUuidUseCase: ListSupplierByUuidUseCase,
    private readonly listSuppliersUseCase: ListSuppliersUseCase,
    private readonly deleteSuppliersUseCase: DeleteSupplierUseCase,
    private readonly updateSupplierUseCase: UpdateSupplierUseCase,
    private readonly createDocumentUseCase: CreateDocumentUseCase,
    private readonly listDocumentsUseCase: ListDocumentsUseCase,
    private readonly downloadDocumentUseCase: DownloadDocumentUseCase,
    private readonly listSupplierContactsUseCase: ListSupplierContactsUseCase,
    private readonly createSupplierContactUseCase: CreateSupplierContactUseCase,
    private readonly updateSupplierContactUseCase: UpdateSupplierContactUseCase,
    private readonly deleteSupplierContactUseCase: DeleteSupplierContactUseCase,
    private readonly getSupplierByUserIdUseCase: GetSupplierByUserIdUsecase,
    @Inject(CreateContractUseCase)
    private readonly createContractUseCase: CreateContractUseCase,
    @Inject(ListContractUseCase)
    private readonly listContractUseCase: ListContractUseCase,
    private readonly downloadContractUseCase: DownloadContractUseCase,
    @Inject(DeleteContractUseCase)
    private readonly deleteContractUseCase: DeleteContractUseCase,
    private readonly prisma: PrismaService,
    private readonly validateSupplierActivationUseCase: ValidateSupplierActivationUseCase,
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,
    @Inject(UpdateContractUseCase)
    private readonly updateContractUseCase: UpdateContractUseCase,
  ) { }

  async listSupplierByUuid(uuid: string): Promise<SupplierResponseDto> {
    const supplier = await this.listSupplierByUuidUseCase.execute(uuid);
    return this.mapToResponseDto(supplier);
  }

  async createSupplier(
    dto: CreateSupplierDto,
    userId: string,
  ): Promise<SupplierResponseDto> {
    const supplier = await this.createSupplierUseCase.execute(dto, userId);
    return this.mapToResponseDto(supplier);
  }

  async listSuppliers(
    params: ListSuppliersDto,
  ): Promise<PaginatedSuppliersResponseDto> {
    const { items, total } = await this.listSuppliersUseCase.execute({
      limit: params.limit || 10,
      offset: params.offset || 0,
      cnpj: params.cnpj,
      name: params.name,
      type: params.type,
      status: params.status,
    });

    return {
      items: items.map((supplier) => this.mapToResponseDto(supplier)),
      total,
      limit: params.limit || 10,
      offset: params.offset || 0,
    };
  }

  async findByUserId(userId: string): Promise<SupplierResponseDto> {
    const supplier = await this.getSupplierByUserIdUseCase.execute(userId);
    return this.mapToResponseDto(supplier);
  }

  async deleteSupplier(uuid: string): Promise<void> {
    await this.deleteSuppliersUseCase.execute({ uuid });
  }

  async updateSupplier(
    uuid: string,
    dto: UpdateSupplierDto,
    userId: string,
  ): Promise<SupplierResponseDto> {
    const updated = await this.updateSupplierUseCase.execute(uuid, dto, userId);
    return this.mapToResponseDto(updated);
  }

  async uploadSupplierDocuments(
    supplierUuid: string,
    files: Express.Multer.File[],
    documentsMetadata: string,
    userId: string,
  ): Promise<Document[]> {
    let metadataArray: unknown[];
    try {
      metadataArray = JSON.parse(documentsMetadata);
    } catch (_error) {
      throw new Error(
        'O campo documentsMetadata não é uma string JSON válida.',
      );
    }

    if (
      !Array.isArray(metadataArray) ||
      files.length !== metadataArray.length
    ) {
      throw new Error(
        'O número de metadados de documento não corresponde ao número de arquivos enviados.',
      );
    }

    const results: Document[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const metadata = metadataArray[i] as Record<string, unknown>;

      const dto: CreateDocumentDto = {
        entityType: EntityType.SUPPLIER,
        entityUuid: supplierUuid,
        uploadedBy: userId,
        responsible: (metadata.responsible as string) || 'N/A',
        department: (metadata.department as string) || 'N/A',
        description: metadata.description as string,
        expirationDate: metadata.expirationDate as string,
      };

      const document = await this.createDocumentUseCase.execute(dto, file);
      results.push(document);
    }

    try {
      await this.validateSupplierActivationUseCase.execute(
        supplierUuid,
        userId,
      );
    } catch (error) {
      console.error(
        '[SupplierService] Erro ao tentar ativar supplier após upload de documentos:',
        error,
      );
    }

    return results;
  }

  async createSupplierContract(
    supplierUuid: string,
    files: Express.Multer.File[],
    userId: string,
    createContractsDto: CreateContractsDto,
  ): Promise<Contract[]> {
    const supplierExists = await this.prisma.supplier.findUnique({
      where: { id: supplierUuid },
    });
    if (!supplierExists) {
      throw new NotFoundException(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    }

    const populatedContracts = createContractsDto.contracts.map((contract) => ({
      ...contract,
      entityActualUuid: supplierUuid,
    }));

    const populatedDto: CreateContractsDto = {
      ...createContractsDto,
      contracts: populatedContracts,
    };

    const contracts = await this.createContractUseCase.execute(
      supplierUuid,
      populatedDto,
      files,
      userId,
    );

    // Tentar ativar o supplier automaticamente após criar contrato
    console.log(
      `[SupplierService] Tentando ativar supplier ${supplierUuid} após criar contrato`,
    );
    try {
      const activated = await this.validateSupplierActivationUseCase.execute(
        supplierUuid,
        userId,
      );
      console.log(
        `[SupplierService] Resultado da ativação após criar contrato: ${activated}`,
      );
    } catch (error) {
      console.error(
        '[SupplierService] Erro ao tentar ativar supplier após criar contrato:',
        error,
      );
    }

    return contracts;
  }

  async listSupplierContracts(supplierUuid: string): Promise<Contract[]> {
    const { items } = await this.listContractUseCase.execute(
      {
        entityType: EntityType.SUPPLIER,
        entityUuid: supplierUuid,
      },
      100,
      0,
    );

    // Gerar URLs de download para todos os contratos
    const contractsWithUrls = await Promise.all(
      items.map(async (contract) => {
        if (contract.versions && contract.versions.length > 0) {
          try {
            // Gerar URL para a versão atual
            const currentVersion = contract.versions.find(v => v.versionId === contract.currentVersion);
            if (currentVersion) {
              const { url, fileName } = await this.downloadContractUseCase.execute(contract.uuid);
              return {
                ...contract,
                downloadUrl: url,
                fileName: fileName,
              };
            }
          } catch (error) {
            console.error(`Erro ao gerar URL de download para contrato ${contract.uuid}:`, error);
          }
        }
        return contract;
      })
    );

    return contractsWithUrls;
  }

  async listSupplierDocuments(supplierUuid: string): Promise<Document[]> {
    const { items } = await this.listDocumentsUseCase.execute({
      entityType: EntityType.SUPPLIER,
      entityUuid: supplierUuid,
    });

    // Gerar URLs de download para todos os documentos
    const documentsWithUrls = await Promise.all(
      items.map(async (document) => {
        if (document.versions && document.versions.length > 0) {
          try {
            // Gerar URL para a versão atual
            const currentVersion = document.versions.find(v => v.versionId === document.currentVersion);
            if (currentVersion) {
              const { url, fileName } = await this.downloadDocumentUseCase.execute(document.uuid);
              return {
                ...document,
                downloadUrl: url,
                fileName: fileName,
              };
            }
          } catch (error) {
            console.error(`Erro ao gerar URL de download para documento ${document.uuid}:`, error);
          }
        }
        return document;
      })
    );

    return documentsWithUrls;
  }

  private mapToResponseDto(supplier: Supplier): SupplierResponseDto {
    const addressJson = supplier.address.toJSON();

    return {
      id: supplier.id,
      name: supplier.name,
      cnpj: supplier.document,
      email: supplier.email,
      type: supplier.type,
      tradeName: supplier.tradeName || undefined,
      address: {
        street: addressJson.street,
        number: addressJson.number || undefined,
        complement: addressJson.complement || undefined,
        neighborhood: addressJson.neighborhood,
        city: addressJson.city,
        zipCode: addressJson.zipCode,
        state: addressJson.state,
      },
      status: supplier.status,
      stateRegistration: supplier.stateRegistration,
      municipalRegistration: supplier.municipalRegistration,
      taxRegime: supplier.taxRegime,
      companySize: supplier.companySize,
      createdAt: supplier.createdAt.toISOString(),
      createdBy: supplier.createdBy,
      updatedAt: supplier.updatedAt.toISOString(),
      updatedBy: supplier.updatedBy,
    };
  }

  getSupplierTypes(): string[] {
    return Object.values(SupplierType);
  }

  async listSupplierContacts(
    supplierUuid: string,
  ): Promise<{ contacts: ContactResponseDto[] }> {
    const contacts =
      await this.listSupplierContactsUseCase.execute(supplierUuid);
    const contactsResponse = contacts.map((contact) => ({
      id: contact.id,
      contact: contact.contact,
      type: contact.type,
      area: contact.area,
      responsible: contact.responsible,
    }));
    return { contacts: contactsResponse };
  }
  
  async createSupplierContacts(
    supplierUuid: string,
    contactsData: Array<{
      contact: string;
      type: string;
      area: string;
      responsible: string;
    }>,
  ): Promise<ContactResponseDto[]> {
    const now = new Date();
    const createdContacts = await Promise.all(
      contactsData.map((contactData) =>
        this.createSupplierContactUseCase.execute(supplierUuid, {
          ...contactData,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
        }),
      ),
    );

    // Tentar ativar o supplier automaticamente após criar contatos
    console.log(
      `[SupplierService] Tentando ativar supplier ${supplierUuid} após criar contatos`,
    );
    try {
      const activated = await this.validateSupplierActivationUseCase.execute(
        supplierUuid,
        'system',
      );
      console.log(
        `[SupplierService] Resultado da ativação após criar contatos: ${activated}`,
      );
    } catch (error) {
      console.error(
        '[SupplierService] Erro ao tentar ativar supplier após criar contatos:',
        error,
      );
    }

    return createdContacts.map((contact) => ({
      contact: contact.contact,
      type: contact.type,
      area: contact.area,
      responsible: contact.responsible,
    }));
  }

  async updateSupplierContacts(
    contactUUID: string,
    data: {
      contact?: string;
      type?: string;
      area?: string;
      responsible?: string;
    },
  ): Promise<ContactResponseDto> {
    const result = await this.updateSupplierContactUseCase.execute(contactUUID, data)
    return result;
  }


  async deleteSupplierContacts(contactUUID: string) {
    return await this.deleteSupplierContactUseCase.execute(contactUUID)
  }

  async deleteSupplierContract(supplierUuid: string, contractUuid: string): Promise<void> {
    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract || contract.entityUuid !== supplierUuid) {
      throw new NotFoundException('Contrato não encontrado para este fornecedor');
    }
    await this.deleteContractUseCase.execute(contractUuid);
  }

  async updateSupplierContract(
    supplierUuid: string,
    contractUuid: string,
    patch: SupplierContractSignPatchDto,
  ): Promise<Contract> {
    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract || contract.entityUuid !== supplierUuid) {
      throw new NotFoundException('Contrato não encontrado para este fornecedor');
    }

    // Cria nova versão do contrato com os dados já existentes, alterando apenas o signed
    await this.contractRepository.createVersion(contractUuid, {
      uploadedBy: contract.createdBy,
      filePath: contract.versions?.find(v => v.versionId === contract.currentVersion)?.filePath || '',
      signed: patch.isSigned,
      expirationDate: (() => {
        const exp = contract.versions?.find(v => v.versionId === contract.currentVersion)?.expirationDate;
        return exp === null ? undefined : exp;
      })(),
    });

    return this.contractRepository.update(contractUuid, {
      status: patch.isSigned ? ContractStatus.APPROVED : ContractStatus.REJECTED,
    });
  }
}
