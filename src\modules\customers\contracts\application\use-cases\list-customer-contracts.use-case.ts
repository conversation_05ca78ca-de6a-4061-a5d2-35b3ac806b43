import { Injectable, Inject } from '@nestjs/common';
import { CustomerContract } from '../../domain/entities/customer-contract.entity';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';
import { CustomerContractResponseDto } from '../../infrastructure/dtos/customer-contract-response.dto';
import { DownloadCustomerContractUseCase } from './download-customer-contract.use-case';

@Injectable()
export class ListCustomerContractsUseCase {
  constructor(
    @Inject('ICustomerContractRepository')
    private readonly repository: ICustomerContractRepository,
    private readonly downloadCustomerContractUseCase: DownloadCustomerContractUseCase,
  ) {}

  async execute(customerUuid: string): Promise<CustomerContractResponseDto[]> {
    const customerContracts = await this.repository.findAllByCustomer(customerUuid);
    
    const contractsWithUrls = await Promise.all(
      customerContracts.map(async (contract) => {
        try {
          // Verificar se o contrato tem arquivo antes de tentar gerar URL
          if (contract.url && contract.url.trim() !== '') {
            const { downloadUrl, fileName } = await this.downloadCustomerContractUseCase.execute(
              customerUuid,
              contract.uuid
            );
            return CustomerContractResponseDto.fromEntity(contract, downloadUrl, fileName);
          } else {
            // Contrato sem arquivo - retorna com campos vazios mas presentes
            const fileName = contract.fileName || `${contract.name}.pdf`;
            return CustomerContractResponseDto.fromEntity(contract, null, fileName);
          }
        } catch (error) {
          console.error('Erro ao gerar URL de download:', error);
          // Retorna com campos de download vazios mas presentes
          const fileName = contract.fileName || `${contract.name}.pdf`;
          return CustomerContractResponseDto.fromEntity(contract, null, fileName);
        }
      })
    );

    return contractsWithUrls;
  }
} 