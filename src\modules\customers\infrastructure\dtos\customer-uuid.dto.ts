import { IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CustomerUuidParamDto {
  @IsUUID(4, { message: 'Invalid UUID format' })
  @ApiProperty({
    description: 'Customer UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  uuid: string;
}

export class CustomerUuidRouteParamDto {
  @IsUUID(4, { message: 'Invalid UUID format' })
  @ApiProperty({
    description: 'Customer UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  customerUuid: string;
}
