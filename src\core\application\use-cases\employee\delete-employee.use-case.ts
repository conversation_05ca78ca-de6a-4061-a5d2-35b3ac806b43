import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  EMPLOYEE_REPOSITORY,
  EmployeeRepositoryPort,
} from '@/core/ports/repositories/employee-repository.port';

export interface DeleteEmployeeInput {
  uuid: string;
}

@Injectable()
export class DeleteEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
  ) {}

  async execute(input: DeleteEmployeeInput): Promise<void> {
    const existingEmployee = await this.employeeRepository.findByUuid(
      input.uuid,
    );

    if (!existingEmployee) {
      throw new NotFoundException('Colaborador não encontrado');
    }

    await this.employeeRepository.delete(input.uuid);
  }
}
