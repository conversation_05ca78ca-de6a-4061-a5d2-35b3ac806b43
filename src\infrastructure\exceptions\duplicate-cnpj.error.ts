import { ConflictException } from '@nestjs/common';

export class DuplicateCnpjError extends ConflictException {
  constructor(cnpj: string, entity: string = 'recurso') {
    super({
      statusCode: 409,
      error: 'Conflict',
      message: `Já existe um ${entity} cadastrado com o CNPJ ${cnpj}. Por favor, verifique os dados e tente novamente.`,
      field: 'cnpj',
      value: cnpj,
      entity: entity,
    });
  }
} 