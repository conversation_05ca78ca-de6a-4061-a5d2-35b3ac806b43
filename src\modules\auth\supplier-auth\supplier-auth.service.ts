import { Injectable } from '@nestjs/common';
import { SupplierService } from '../../finance/services/supplier.service';
import { Role } from '@/core/domain/role.enum';

@Injectable()
export class SupplierAuthService {
  constructor(private readonly supplierService: SupplierService) {}

  async getSupplierInfo(userId: string, role: string): Promise<any> {
    if (role === Role.SUPPLIER_VIEWER) {
      return this.supplierService.findByUserId(userId);
    }
    return null;
  }
} 