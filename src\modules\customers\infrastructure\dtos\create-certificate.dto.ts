import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';

export class CreateCertificateDto {
  @ApiProperty({
    description: 'Categoria do certificado',
    enum: CertificateCategory,
    example: CertificateCategory.FORNECEDORES_JOGOS,
  })
  @IsEnum(CertificateCategory)
  @IsNotEmpty()
  category: CertificateCategory;

  @ApiProperty({
    description: 'Tipo do certificado',
    enum: CertificateType,
    example: CertificateType.CERTIFICADO_DE_JOGO,
  })
  @IsEnum(CertificateType)
  @IsNotEmpty()
  type: CertificateType;

  @ApiProperty({
    description: 'Observações sobre o certificado (opcional)',
    example: 'Válido até 2025',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
} 