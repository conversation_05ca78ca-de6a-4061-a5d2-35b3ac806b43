import { Injectable } from '@nestjs/common';
import { PaymentMethod } from '../../core/domain/payment-method';
import { PaymentMethodRepository } from '../../core/ports/repositories/payment-method-repository.interface';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '@prisma/client';

type PrismaPaymentMethod = {
  id: number;
  uuid: string;
  label: string;
  description: string | null;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  updatedBy: string;
  deletedAt?: Date | null;
};

@Injectable()
export class PrismaPaymentMethodRepository implements PaymentMethodRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(paymentMethod: PaymentMethod): Promise<PaymentMethod> {
    const created = await this.prisma.paymentMethod.create({
      data: {
        uuid: paymentMethod.uuid,
        label: paymentMethod.label,
        description: paymentMethod.description,
        createdBy: paymentMethod.createdBy,
        updatedBy: paymentMethod.updatedBy,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
      },
    });
    return this.toDomain(created);
  }

  async findWithPagination({
    limit,
    offset,
    label,
    id,
  }: {
    limit: number;
    offset: number;
    label?: string;
    id?: string;
  }): Promise<{ items: PaymentMethod[]; total: number }> {
    let where: Prisma.PaymentMethodWhereInput = { deletedAt: null };
    if (id) {
      where = { ...where, uuid: id };
    } else if (label) {
      where = { ...where, label: { contains: label } };
    }
    const [items, total] = await this.prisma.$transaction([
      this.prisma.paymentMethod.findMany({
        where,
        take: limit,
        skip: offset,
      }),
      this.prisma.paymentMethod.count({ where }),
    ]);
    return { items: items.map((pm) => this.toDomain(pm)), total };
  }

  async findById(id: number): Promise<PaymentMethod | null> {
    const found = await this.prisma.paymentMethod.findUnique({
      where: { id: id },
    });
    if (!found || found.deletedAt) return null;
    return this.toDomain(found);
  }

  async findByUuid(uuid: string): Promise<PaymentMethod | null> {
    const found = await this.prisma.paymentMethod.findUnique({
      where: { uuid: uuid },
    });
    if (!found || found.deletedAt) return null;
    return this.toDomain(found);
  }

  async findByLabel(label: string): Promise<PaymentMethod | null> {
    const found = await this.prisma.paymentMethod.findFirst({
      where: { label: label, deletedAt: null },
    });
    if (!found) return null;
    return this.toDomain(found);
  }

  async findAll(): Promise<PaymentMethod[]> {
    const paymentMethods = await this.prisma.paymentMethod.findMany({
      where: { deletedAt: null },
    });
    return paymentMethods.map((pm) => this.toDomain(pm));
  }

  async update(paymentMethod: PaymentMethod): Promise<PaymentMethod> {
    const updated = await this.prisma.paymentMethod.update({
      where: { uuid: paymentMethod.uuid },
      data: {
        label: paymentMethod.label,
        description: paymentMethod.description,
        updatedBy: paymentMethod.updatedBy,
        updatedAt: paymentMethod.updatedAt,
      },
    });
    return this.toDomain(updated);
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.paymentMethod.update({
      where: { uuid: uuid },
      data: { deletedAt: new Date() },
    });
  }

  private toDomain(paymentMethod: PrismaPaymentMethod): PaymentMethod {
    return new PaymentMethod(
      paymentMethod.id,
      paymentMethod.uuid,
      paymentMethod.label,
      paymentMethod.description || '',
      paymentMethod.createdBy,
      paymentMethod.createdAt,
      paymentMethod.updatedAt,
      paymentMethod.updatedBy,
    );
  }
}
