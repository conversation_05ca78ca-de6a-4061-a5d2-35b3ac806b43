import { SupplierType } from './supplier-type.enum';

describe('SupplierType Enum', () => {
  it('should have the correct values', () => {
    expect(SupplierType.BETTING).toBe('BETTING');
    expect(SupplierType.CASINO).toBe('CASINO');
    expect(SupplierType.EGAMES).toBe('EGAMES');
    expect(SupplierType.GAMES).toBe('GAMES');
    expect(SupplierType.LOTTERY).toBe('LOTTERY');
    expect(SupplierType.POKER).toBe('POKER');
    expect(SupplierType.SPORTS).toBe('SPORTS');
    expect(SupplierType.OTHER).toBe('OTHER');
  });

  it('should have exactly eight status options', () => {
    const statusValues = Object.values(SupplierType);
    expect(statusValues).toHaveLength(8);
    expect(statusValues).toContain('BETTING');
    expect(statusValues).toContain('CASINO');
    expect(statusValues).toContain('EGAMES');
    expect(statusValues).toContain('GAMES');
    expect(statusValues).toContain('LOTTERY');
    expect(statusValues).toContain('POKER');
    expect(statusValues).toContain('SPORTS');
    expect(statusValues).toContain('OTHER');
  });

  it('should have the correct keys', () => {
    const statusKeys = Object.keys(SupplierType);
    expect(statusKeys).toHaveLength(8);
    expect(statusKeys).toContain('OTHER');
    expect(statusKeys).toContain('EGAMES');
    expect(statusKeys).toContain('GAMES');
    expect(statusKeys).toContain('POKER');
    expect(statusKeys).toContain('SPORTS');
    expect(statusKeys).toContain('CASINO');
    expect(statusKeys).toContain('LOTTERY');
    expect(statusKeys).toContain('BETTING');
  });

  it('should not allow invalid status values', () => {
    const validStatuses = Object.values(SupplierType);
    const invalidStatus = 'clothing' as SupplierType;

    expect(validStatuses).not.toContain(invalidStatus);
  });
});
