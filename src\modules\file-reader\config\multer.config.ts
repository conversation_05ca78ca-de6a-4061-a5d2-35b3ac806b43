import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { BadRequestException } from '@nestjs/common';

export const fileUploadConfig: MulterOptions = {
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1, // Only one file at a time
  },
  fileFilter: (req, file, callback) => {
    // Check file extension
    const allowedExtensions = /\.(csv|xlsx|xls)$/i;
    if (!file.originalname.match(allowedExtensions)) {
      return callback(
        new BadRequestException(
          'Only CSV and Excel files are allowed. Supported formats: .csv, .xlsx, .xls'
        ),
        false
      );
    }

    // Check MIME type
    const allowedMimeTypes = [
      'text/csv',
      'application/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return callback(
        new BadRequestException(
          `Invalid file type: ${file.mimetype}. Please upload a valid CSV or Excel file.`
        ),
        false
      );
    }

    callback(null, true);
  },
};

export const csvOnlyConfig: MulterOptions = {
  ...fileUploadConfig,
  fileFilter: (req, file, callback) => {
    if (!file.originalname.match(/\.csv$/i)) {
      return callback(
        new BadRequestException('Only CSV files are allowed'),
        false
      );
    }

    const allowedMimeTypes = ['text/csv', 'application/csv'];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      return callback(
        new BadRequestException(`Invalid CSV file type: ${file.mimetype}`),
        false
      );
    }

    callback(null, true);
  },
};

export const excelOnlyConfig: MulterOptions = {
  ...fileUploadConfig,
  fileFilter: (req, file, callback) => {
    if (!file.originalname.match(/\.(xlsx|xls)$/i)) {
      return callback(
        new BadRequestException('Only Excel files are allowed'),
        false
      );
    }

    const allowedMimeTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return callback(
        new BadRequestException(`Invalid Excel file type: ${file.mimetype}`),
        false
      );
    }

    callback(null, true);
  },
};

export const largeFileConfig: MulterOptions = {
  ...fileUploadConfig,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB for large files
    files: 1,
  },
};
