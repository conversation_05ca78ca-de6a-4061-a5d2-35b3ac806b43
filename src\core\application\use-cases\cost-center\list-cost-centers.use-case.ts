import { Inject, Injectable } from '@nestjs/common';
import {
  COST_CENTER_REPOSITORY,
  CostCenterRepositoryPort,
} from '@core/ports/repositories/cost-center-repository.port';
import { CostCenter } from '@core/domain/cost-center/entities/cost-center.entity';

export interface ListCostCentersParams {
  limit?: number;
  offset?: number;
  description?: string;
}

export interface ListCostCentersResult {
  items: CostCenter[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable()
export class ListCostCentersUseCase {
  constructor(
    @Inject(COST_CENTER_REPOSITORY)
    private readonly costCenterRepository: CostCenterRepositoryPort,
  ) {}

  async execute(
    params: ListCostCentersParams = {},
  ): Promise<ListCostCentersResult> {
    const limit = params.limit || 10;
    const offset = params.offset || 0;

    const { items, total } = await this.costCenterRepository.findWithPagination(
      {
        limit,
        offset,
        description: params.description,
      },
    );

    return {
      items,
      total,
      limit,
      offset,
    };
  }
}
