import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { SectorRepositoryPort } from '@core/ports/repositories/sector-repository.port';
import { DeleteEmployeeUseCase } from './delete-employee.use-case';
import {
  EMPLOYEE_REPOSITORY,
  EmployeeRepositoryPort,
} from '@/core/ports/repositories/employee-repository.port';
import { Employee } from '@/core/domain/entities/employee.entity';
import { EmployeeStatus } from '@prisma/client';

describe('DeleteEmployeeUseCase', () => {
  let useCase: DeleteEmployeeUseCase;
  let repository: jest.Mocked<EmployeeRepositoryPort>;

  const mockEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-426614174000',
    '<PERSON>',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [],
    EmployeeStatus.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SectorRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findByUuid: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
      findByCode: jest.fn(),
      list: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteEmployeeUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<DeleteEmployeeUseCase>(DeleteEmployeeUseCase);
    repository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  describe('execute', () => {
    it('should delete employee when found by UUID', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');
      const deleteFn = jest.spyOn(repository, 'delete');

      findByUuid.mockResolvedValue(mockEmployee);

      await useCase.execute({ uuid: 'test-uuid' });

      expect(findByUuid).toHaveBeenCalledWith('test-uuid');
      expect(deleteFn).toHaveBeenCalledWith('test-uuid');
    });

    it('should throw EmployeeNotFoundError when employee does not exist', async () => {
      const findByUuid = jest.spyOn(repository, 'findByUuid');

      findByUuid.mockResolvedValue(null);

      await expect(
        useCase.execute({ uuid: 'non-existent-uuid' }),
      ).rejects.toThrow(NotFoundException);
      expect(findByUuid).toHaveBeenCalledWith('non-existent-uuid');
    });
  });
});
