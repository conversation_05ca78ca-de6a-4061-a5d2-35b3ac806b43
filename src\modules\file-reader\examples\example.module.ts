import { Module } from '@nestjs/common';
import { FileReaderModule } from '../file-reader.module';
import { FileUploadController } from './file-upload.controller';
import { CustomersImportController } from './customers-import.controller';
import { BatchProcessingController } from './batch-processing.controller';

@Module({
  imports: [FileReaderModule],
  controllers: [
    FileUploadController,
    CustomersImportController,
    BatchProcessingController,
  ],
})
export class FileUploadExampleModule {}
