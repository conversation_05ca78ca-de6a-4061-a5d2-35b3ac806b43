import { Injectable, Inject } from '@nestjs/common';
import { CustomerPaymentPreference } from '../../domain/entities/customer-payment-preference.entity';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';

@Injectable()
export class ListCustomerPaymentPreferencesUseCase {
  constructor(
    @Inject('ICustomerPaymentPreferenceRepository')
    private readonly repository: ICustomerPaymentPreferenceRepository,
  ) {}

  async execute(): Promise<CustomerPaymentPreference[]> {
    return this.repository.findAll();
  }
} 