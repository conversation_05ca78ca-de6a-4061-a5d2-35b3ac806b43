import { NotFoundException } from '@nestjs/common';
import { DownloadDocumentUseCase } from '../../application/use-cases/download-document.use-case';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { IDocumentRepository } from '../../domain/repositories/document.repository.interface';
import { Document } from '../../domain/entities/document.entity';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { DocumentStatus } from '../../domain/enums/document-status.enum';

describe('DownloadDocumentUseCase', () => {
  let useCase: DownloadDocumentUseCase;
  let documentRepository: jest.Mocked<IDocumentRepository>;
  let storageProvider: jest.Mocked<IStorageProvider>;

  const uuid = 'document-uuid';
  const versionId = 2;

  const fakeDocument = {
    uuid,
    entityType: EntityType.CLIENT,
    entityUuid: 'entity-uuid',
    currentVersion: 1,
    status: DocumentStatus.ACTIVE,
    uploadedBy: 'user-uuid',
    createdAt: new Date(),
    updatedAt: new Date(),
    versions: [
      {
        versionId: 1,
        uploadedBy: 'user-uuid',
        filePath: 'path/to/file-v1.pdf',
        createdAt: new Date(),
      },
      {
        versionId: 2,
        uploadedBy: 'user-uuid',
        filePath: 'path/to/file-v2.pdf',
        createdAt: new Date(),
      },
    ],
  } as Required<Document>;

  beforeEach(() => {
    documentRepository = {
      findByUuid: jest.fn(),
      create: jest.fn(),
      list: jest.fn(),
      updateStatusToArchived: jest.fn(),
    };

    storageProvider = {
      upload: jest.fn(),
      getFileStream: jest.fn(),
    };

    useCase = new DownloadDocumentUseCase(documentRepository, storageProvider);
  });

  it('deve lançar NotFoundException se o documento não for encontrado', async () => {
    documentRepository.findByUuid.mockResolvedValue(null);

    await expect(() => useCase.execute(uuid)).rejects.toThrow(
      NotFoundException,
    );
  });

  it('deve lançar NotFoundException se a versão não for encontrada', async () => {
    documentRepository.findByUuid.mockResolvedValue({
      ...fakeDocument,
      versions: [],
    });

    await expect(() => useCase.execute(uuid, versionId)).rejects.toThrow(
      NotFoundException,
    );
  });
});
