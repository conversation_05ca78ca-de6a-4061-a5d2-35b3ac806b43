import { validate } from 'class-validator';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';

describe('ForgotPasswordDto validation', () => {
  it('should fail if email is empty', async () => {
    const dto = new ForgotPasswordDto();
    dto.email = '';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail if email is not provided', async () => {
    const dto = new ForgotPasswordDto();
    // email não definido
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('should fail if email is invalid', async () => {
    const dto = new ForgotPasswordDto();
    dto.email = 'invalid-email';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEmail');
  });

  it('should pass with valid email', async () => {
    const dto = new ForgotPasswordDto();
    dto.email = '<EMAIL>';
    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });
});
