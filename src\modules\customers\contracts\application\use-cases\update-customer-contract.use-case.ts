import { Injectable, Inject } from '@nestjs/common';
import { ContractDto } from '../../infrastructure/dtos/customercontract.dto';
import { CustomerContract } from '../../domain/entities/customer-contract.entity';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';

@Injectable()
export class UpdateCustomerContractUseCase {
  constructor(
    @Inject('ICustomerContractRepository')
    private readonly repository: ICustomerContractRepository,
  ) {}

  async execute(customerUuid: string, contractUuid: string, dto: Partial<ContractDto>): Promise<CustomerContract> {
    return this.repository.update(customerUuid, contractUuid, dto);
  }
} 