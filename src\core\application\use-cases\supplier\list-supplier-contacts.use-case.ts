import { Injectable, Inject } from '@nestjs/common';
import { SupplierContact } from '../../../domain/supplier/entities/supplier-contact.entity';
import { SupplierContactRepositoryPort } from '../../../ports/repositories/supplier-contact-repository.port';

@Injectable()
export class ListSupplierContactsUseCase {
  constructor(
    @Inject('SupplierContactRepository')
    private readonly supplierContactRepository: SupplierContactRepositoryPort,
  ) { }

  async execute(supplierId: string): Promise<SupplierContact[]> {
    return this.supplierContactRepository.findBySupplierId(supplierId);
  }
} 