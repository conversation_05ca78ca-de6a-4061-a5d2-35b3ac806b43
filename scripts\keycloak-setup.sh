#!/bin/bash
set -e

# Configurações
KEYCLOAK_URL="http://localhost:8080"
REALM="master"
ADMIN_USER="admin"
ADMIN_PASS="admin"
CLIENT_ID="backend-dev-client"
CLIENT_SECRET="myclientsecret"
TEST_USER="testuser"
TEST_PASS="testpass"
ROLES=(
  "ADMIN"
  "USER"
  "FINANCE_ADMIN"
  "FINANCE_USER"
  "DOCUMENT_ARCHIVER"
  "DOCUMENT_UPLOADER"
  "DOCUMENT_VIEWER"
  "SUPPLIER_VIEWER"
  "CUSTOMER_VIEWER"
  "EMPLOYEE"
)

# Função para obter token de admin
get_admin_token() {
  curl -s \
    -d "grant_type=password" \
    -d "client_id=admin-cli" \
    -d "username=$ADMIN_USER" \
    -d "password=$ADMIN_PASS" \
    "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
    | jq -r .access_token
}

# Função para checar se realm existe
ensure_realm_exists() {
  local token="$1"
  local exists=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $token" "$KEYCLOAK_URL/admin/realms/$REALM")
  if [ "$exists" == "200" ]; then
    echo "Realm '$REALM' já existe."
  else
    curl -s -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
      -d '{"realm":"'$REALM'","enabled":true}' \
      "$KEYCLOAK_URL/admin/realms"
    echo "Realm '$REALM' criado."
  fi
}

# Função para criar role se não existir
ensure_role_exists() {
  local role="$1"
  local token="$2"
  local exists=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $token" "$KEYCLOAK_URL/admin/realms/$REALM/roles/$role")
  if [ "$exists" == "200" ]; then
    echo "Role '$role' já existe."
  else
    curl -s -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
      -d '{"name":"'$role'","description":"Role '$role' do domínio"}' \
      "$KEYCLOAK_URL/admin/realms/$REALM/roles"
    echo "Role '$role' criada."
  fi
}

# Função para criar todas as roles
ensure_all_domain_roles() {
  local token="$1"
  for role in "${ROLES[@]}"; do
    ensure_role_exists "$role" "$token"
  done
}

# Função para criar client se não existir
ensure_client_exists() {
  local token="$1"
  local clients=$(curl -s -H "Authorization: Bearer $token" "$KEYCLOAK_URL/admin/realms/$REALM/clients?clientId=$CLIENT_ID")
  local client_id=$(echo "$clients" | jq -r '.[0].id // empty')
  if [ -z "$client_id" ]; then
    curl -s -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
      -d '{"clientId":"'$CLIENT_ID'","enabled":true,"secret":"'$CLIENT_SECRET'","directAccessGrantsEnabled":true,"serviceAccountsEnabled":true,"publicClient":false,"protocol":"openid-connect"}' \
      "$KEYCLOAK_URL/admin/realms/$REALM/clients"
    echo "Client '$CLIENT_ID' criado."
  else
    echo "Client '$CLIENT_ID' já existe."
    # Atualiza secret e configs
    curl -s -X PUT -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
      -d '{"secret":"'$CLIENT_SECRET'","directAccessGrantsEnabled":true,"serviceAccountsEnabled":true,"publicClient":false,"protocol":"openid-connect"}' \
      "$KEYCLOAK_URL/admin/realms/$REALM/clients/$client_id"
  fi
}

# Função para criar usuário se não existir
ensure_user_exists() {
  local token="$1"
  local users=$(curl -s -H "Authorization: Bearer $token" "$KEYCLOAK_URL/admin/realms/$REALM/users?username=$TEST_USER")
  local user_id=$(echo "$users" | jq -r '.[0].id // empty')
  if [ -z "$user_id" ]; then
    curl -s -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
      -d '{"username":"'$TEST_USER'","enabled":true,"emailVerified":true,"email":"'$TEST_USER'@test.com"}' \
      "$KEYCLOAK_URL/admin/realms/$REALM/users"
    echo "Usuário '$TEST_USER' criado."
    # Buscar id do usuário criado
    users=$(curl -s -H "Authorization: Bearer $token" "$KEYCLOAK_URL/admin/realms/$REALM/users?username=$TEST_USER")
    user_id=$(echo "$users" | jq -r '.[0].id')
  else
    echo "Usuário '$TEST_USER' já existe."
    # Atualiza email
    curl -s -X PUT -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
      -d '{"email":"'$TEST_USER'@test.com","emailVerified":true}' \
      "$KEYCLOAK_URL/admin/realms/$REALM/users/$user_id"
  fi
  # Define senha
  curl -s -X PUT -H "Authorization: Bearer $token" -H "Content-Type: application/json" \
    -d '{"type":"password","value":"'$TEST_PASS'","temporary":false}' \
    "$KEYCLOAK_URL/admin/realms/$REALM/users/$user_id/reset-password"
}

# Função para validar client e usuário
validate_client_and_user() {
  local params="grant_type=password&client_id=$CLIENT_ID&client_secret=$CLIENT_SECRET&username=$TEST_USER&password=$TEST_PASS"
  local res=$(curl -s -w "%{http_code}" -o /tmp/keycloak_token.json -d "$params" "$KEYCLOAK_URL/realms/$REALM/protocol/openid-connect/token")
  if grep -q 'access_token' /tmp/keycloak_token.json; then
    echo "Validação do client e usuário OK."
  else
    echo "Erro ao validar client/usuário:"
    cat /tmp/keycloak_token.json
    exit 1
  fi
  rm -f /tmp/keycloak_token.json
}

# Execução principal
token=$(get_admin_token)
ensure_realm_exists "$token"
ensure_all_domain_roles "$token"
ensure_client_exists "$token"
ensure_user_exists "$token"
validate_client_and_user

echo "Setup do Keycloak finalizado com sucesso!" 