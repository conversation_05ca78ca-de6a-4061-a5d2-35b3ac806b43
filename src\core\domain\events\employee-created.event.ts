import { DomainEvent } from '../base/domain-event';
import { Employee } from '../entities/employee.entity';

export class EmployeeCreatedEvent extends DomainEvent {
  constructor(private readonly employee: Employee) {
    super('employee.created');
  }

  getEventName(): string {
    return 'employee.created';
  }

  getData(): object {
    return {
      employeeId: this.employee.id,
      uuid: this.employee.uuid,
      name: this.employee.name,
      email: this.employee.email,
      position: this.employee.position,
      department: this.employee.department,
      hireDate: this.employee.hireDate.toISOString(),
      address: this.employee.address,
      personalDocuments: this.employee.personalDocuments,
      dependents: this.employee.dependents,
      status: this.employee.status,
      createdBy: this.employee.createdBy,
      updatedBy: this.employee.updatedBy,
      createdAt: this.employee.createdAt.toISOString(),
      updatedAt: this.employee.updatedAt.toISOString(),
    };
  }
}
