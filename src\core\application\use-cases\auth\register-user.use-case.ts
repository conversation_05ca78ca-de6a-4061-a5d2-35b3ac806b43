import { UserRepository } from '../../../ports/repositories/user-repository.interface';
import { User } from '../../../domain/user.entity';
import { v4 as uuidv4 } from 'uuid';
import { Role } from '../../../domain/role.enum';
import { PasswordHasher } from '../../../shared/password-hasher';

export interface RegisterUserInput {
  name: string;
  email: string;
  password: string;
}

export interface RegisterUserOutput {
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
  };
}

export class RegisterUserUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(input: RegisterUserInput): Promise<RegisterUserOutput> {
    const { name, email, password } = input;

    // Verificar se já existe um usuário com o mesmo email
    const existingUser = await this.userRepository
      .findByEmail(email)
      .catch(() => null);

    if (existingUser) {
      throw new Error(`Usuário com email ${email} já existe`);
    }

    // Hash da senha
    const hashedPassword = await PasswordHasher.hash(password);

    // Criar a entidade de domínio
    const user = new User(uuidv4(), email, name, hashedPassword, Role.USER);

    // Persistir no repositório
    const createdUser = await this.userRepository.create(user);

    // Retornar os dados do usuário registrado
    return {
      user: {
        id: createdUser.id,
        email: createdUser.email,
        name: createdUser.name,
        role: createdUser.role,
      },
    };
  }
}
