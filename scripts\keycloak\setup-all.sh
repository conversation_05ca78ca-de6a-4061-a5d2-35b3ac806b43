#!/bin/bash

# Carregar variáveis de ambiente
source "$(dirname "$(dirname "$(dirname "$0")")")/.env.test"

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

ROLES=(ADMIN USER FINANCE_ADMIN FINANCE_USER DOCUMENT_ARCHIVER DOCUMENT_UPLOADER DOCUMENT_VIEWER DOCUMENT_DOWNLOADER )

echo -e "${YELLOW}🔑 Obtendo access_token de admin...${NC}"
ADMIN_TOKEN=$(curl -s -X POST "${KEYCLOAK_BASE_URL_LOCALHOST}/realms/${KEYCLOAK_REALM}/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=${KEYCLOAK_ADMIN_CLIENT_ID}" \
  -d "username=${KEYCLOAK_ADMIN_USERNAME}" \
  -d "password=${KEYCLOAK_ADMIN_PASSWORD}" \
  -d "grant_type=password" | jq -r .access_token)

if [ -z "$ADMIN_TOKEN" ] || [ "$ADMIN_TOKEN" == "null" ]; then
  echo -e "${RED}❌ Falha ao obter token admin${NC}"
  exit 1
fi

echo -e "${YELLOW}📝 Verificando client 'backoffice-app'...${NC}"
CLIENT_ID=$(curl -s -X GET "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/clients?clientId=${KEYCLOAK_CLIENT_ID}" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq -r '.[0].id')

if [ -z "$CLIENT_ID" ] || [ "$CLIENT_ID" == "null" ]; then
  echo -e "${YELLOW}📝 Criando client 'backoffice-app'...${NC}"
  curl -s -X POST "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/clients" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "clientId": "'${KEYCLOAK_CLIENT_ID}'",
      "secret": "'${KEYCLOAK_CLIENT_SECRET}'",
      "enabled": true,
      "protocol": "openid-connect",
      "publicClient": false,
      "standardFlowEnabled": '${KEYCLOAK_CLIENT_STANDARD_FLOW_ENABLED}',
      "implicitFlowEnabled": '${KEYCLOAK_CLIENT_IMPLICIT_FLOW_ENABLED}',
      "directAccessGrantsEnabled": '${KEYCLOAK_CLIENT_DIRECT_ACCESS_GRANTS_ENABLED}',
      "serviceAccountsEnabled": '${KEYCLOAK_CLIENT_SERVICE_ACCOUNTS_ENABLED}',
      "fullScopeAllowed": '${KEYCLOAK_CLIENT_FULL_SCOPE_ALLOWED}',
      "attributes": {
        "access.token.lifespan": "3600",
        "backchannel.logout.session.required": "true",
        "backchannel.logout.revoke.offline.tokens": "false"
      },
      "redirectUris": [
        "http://localhost:3000/*"
      ],
      "webOrigins": [
        "http://localhost:3000"
      ]
    }'
  echo -e "${GREEN}✅ Client 'backoffice-app' criado${NC}"
  
  # Get the client ID after creation
  CLIENT_ID=$(curl -s -X GET "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/clients?clientId=${KEYCLOAK_CLIENT_ID}" \
    -H "Authorization: Bearer $ADMIN_TOKEN" | jq -r '.[0].id')
else
  echo -e "${YELLOW}ℹ️ Client 'backoffice-app' já existe, usando ID existente${NC}"
fi

echo -e "${GREEN}✅ Client ID: $CLIENT_ID${NC}"

echo -e "${YELLOW}👥 Criando roles no client...${NC}"
for ROLE in "${ROLES[@]}"; do
  # Check if role exists
  ROLE_EXISTS=$(curl -s -X GET "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/clients/$CLIENT_ID/roles/$ROLE" \
    -H "Authorization: Bearer $ADMIN_TOKEN" | jq -r '.name // empty')
  
  if [ -z "$ROLE_EXISTS" ]; then
    echo -e "${YELLOW}📝 Criando role '$ROLE'...${NC}"
    curl -s -X POST "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/clients/$CLIENT_ID/roles" \
      -H "Authorization: Bearer $ADMIN_TOKEN" \
      -H "Content-Type: application/json" \
      -d '{"name": "'$ROLE'", "description": "Role '$ROLE' do schema.prisma"}'
    echo -e "${GREEN}✅ Role '$ROLE' criada${NC}"
  else
    echo -e "${YELLOW}ℹ️ Role '$ROLE' já existe, pulando...${NC}"
  fi
done

echo -e "${GREEN}✅ Roles verificadas/criadas${NC}"

# Descobrir o ID do usuário admin
USER_ID=$(curl -s -X GET "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/users?username=${KEYCLOAK_ADMIN_USERNAME}" \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq -r '.[0].id')

if [ -z "$USER_ID" ] || [ "$USER_ID" == "null" ]; then
  echo -e "${RED}❌ Falha ao obter user ID do admin${NC}"
  exit 1
fi

echo -e "${GREEN}✅ User ID do admin: $USER_ID${NC}"

echo -e "${YELLOW}🔗 Atribuindo roles ao usuário admin...${NC}"
for ROLE in "${ROLES[@]}"; do
  ROLE_OBJ=$(curl -s -X GET "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/clients/$CLIENT_ID/roles/$ROLE" \
    -H "Authorization: Bearer $ADMIN_TOKEN")
  curl -s -X POST "${KEYCLOAK_BASE_URL_LOCALHOST}/admin/realms/${KEYCLOAK_REALM}/users/$USER_ID/role-mappings/clients/$CLIENT_ID" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d "[$ROLE_OBJ]"
done

echo -e "${GREEN}🎉 Roles criadas e atribuídas ao usuário admin com sucesso!${NC}" 