import { PaymentMethod } from '../../domain/payment-method';

export interface PaymentMethodRepository {
  findWithPagination(params: {
    limit: number;
    offset: number;
    label?: string;
    id?: string;
  }): Promise<{ items: PaymentMethod[]; total: number }>;
  create(paymentMethod: PaymentMethod): Promise<PaymentMethod>;
  findById(id: number): Promise<PaymentMethod | null>;
  findByUuid(uuid: string): Promise<PaymentMethod | null>;
  findByLabel(label: string): Promise<PaymentMethod | null>;
  findAll(): Promise<PaymentMethod[]>;
  update(paymentMethod: PaymentMethod): Promise<PaymentMethod>;
  delete(uuid: string): Promise<void>;
}
