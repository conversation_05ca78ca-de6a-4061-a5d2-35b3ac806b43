import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { ICustomerPaymentPreferenceRepository } from '../../domain/repositories/customer-payment-preference.repository.interface';
import { CustomerPaymentPreference } from '../../domain/entities/customer-payment-preference.entity';

@Injectable()
export class CustomerPaymentPreferenceRepository implements ICustomerPaymentPreferenceRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: Partial<CustomerPaymentPreference>): Promise<CustomerPaymentPreference> {
    const result = await this.prisma.customerPaymentPreference.create({
      data: {
        customerId: data.customerId || '',
        department: data.department || '',
        responsible: 'Default Responsible',
        paymentMethodId: data.paymentMethodId || '',
        paymentCondition: 'Default Payment Condition',
        billingPreference: 'Default Billing Preference',
        costCenterId: data.costCenterId || '',
        sectorId: data.sectorId || '',
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      },
    });
    
    return new CustomerPaymentPreference({
      id: result.id,
      customerId: result.customerId,
      paymentMethodId: result.paymentMethodId,
      department: result.department,
      costCenterId: result.costCenterId,
      sectorId: result.sectorId,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    });
  }

  async findAll(): Promise<CustomerPaymentPreference[]> {
    const results = await this.prisma.customerPaymentPreference.findMany();
    return results.map(result => new CustomerPaymentPreference({
      id: result.id,
      customerId: result.customerId,
      paymentMethodId: result.paymentMethodId,
      department: result.department,
      costCenterId: result.costCenterId,
      sectorId: result.sectorId,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }));
  }

  async findById(id: string): Promise<CustomerPaymentPreference | null> {
    const result = await this.prisma.customerPaymentPreference.findUnique({
      where: { id },
    });

    return result ? new CustomerPaymentPreference({
      id: result.id,
      customerId: result.customerId,
      paymentMethodId: result.paymentMethodId,
      department: result.department,
      costCenterId: result.costCenterId,
      sectorId: result.sectorId,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }) : null;
  }

  async findByCustomerId(customerId: string): Promise<CustomerPaymentPreference[]> {
    const results = await this.prisma.customerPaymentPreference.findMany({
      where: { customerId },
    });

    return results.map(result => new CustomerPaymentPreference({
      id: result.id,
      customerId: result.customerId,
      paymentMethodId: result.paymentMethodId,
      department: result.department,
      costCenterId: result.costCenterId,
      sectorId: result.sectorId,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }));
  }

  async update(id: string, data: CustomerPaymentPreference): Promise<CustomerPaymentPreference> {
    const result = await this.prisma.customerPaymentPreference.update({
      where: { id },
      data: {
        paymentMethodId: data.paymentMethodId,
        department: data.department,
        costCenterId: data.costCenterId,
        sectorId: data.sectorId,
        updatedAt: data.updatedAt,
      },
    });

    return new CustomerPaymentPreference({
      id: result.id,
      customerId: result.customerId,
      paymentMethodId: result.paymentMethodId,
      department: result.department,
      costCenterId: result.costCenterId,
      sectorId: result.sectorId,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.customerPaymentPreference.delete({
      where: { id },
    });
  }
} 