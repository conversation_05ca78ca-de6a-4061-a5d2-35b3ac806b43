import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { PaymentMethodRepository } from '../../../ports/repositories/payment-method-repository.interface';
import { PaymentMethod } from '../../../domain/payment-method';

export interface UpdatePaymentMethodInput {
  uuid: string;
  paymentMethod: {
    label?: string;
    description?: string;
  };
  updatedBy: string;
}

@Injectable()
export class UpdatePaymentMethodUseCase {
  constructor(
    @Inject('PaymentMethodRepository')
    private readonly paymentMethodRepository: PaymentMethodRepository,
  ) {}

  async execute(input: UpdatePaymentMethodInput): Promise<PaymentMethod> {
    const existingPaymentMethod = await this.paymentMethodRepository.findByUuid(
      input.uuid,
    );

    if (!existingPaymentMethod) {
      throw new NotFoundException('Método de pagamento não encontrado');
    }

    const existingPaymentMethodLabel = input.paymentMethod.label
      ? await this.paymentMethodRepository.findByLabel(
          input.paymentMethod.label,
        )
      : null;
    if (
      existingPaymentMethodLabel &&
      existingPaymentMethodLabel.uuid !== input.uuid
    ) {
      throw new NotFoundException(
        'Já existe um método de pagamento com este nome',
      );
    }

    const updatedPaymentMethod = {
      ...existingPaymentMethod,
      ...input.paymentMethod,
      updatedBy: input.updatedBy,
      updatedAt: new Date(),
    } as PaymentMethod;

    return await this.paymentMethodRepository.update(updatedPaymentMethod);
  }
}
