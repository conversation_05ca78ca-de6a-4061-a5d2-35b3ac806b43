import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';

@Injectable()
export class SnsService {
  private readonly client: SNSClient;
  private readonly logger = new Logger(SnsService.name);
  private readonly topicArn: string;

  constructor(private readonly configService: ConfigService) {
    this.client = new SNSClient({
      region: this.configService.get<string>('AWS_REGION') || 'us-east-1',
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID') || '',
        secretAccessKey:
          this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || '',
      },
    });
    this.topicArn = this.configService.get<string>('AWS_SNS_TOPIC_ARN') || '';
  }

  async publish<T>(message: T, subject?: string): Promise<void> {
    try {
      const command = new PublishCommand({
        TopicArn: this.topicArn,
        Message: JSON.stringify(message),
        Subject: subject,
        MessageAttributes: {
          timestamp: {
            DataType: 'String',
            StringValue: new Date().toISOString(),
          },
        },
      });

      const response = await this.client.send(command);
      this.logger.log(
        `Mensagem publicada no SNS com ID: ${response.MessageId}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Erro ao publicar mensagem no SNS: ${error.message}`);
      } else {
        this.logger.error(
          'Erro ao publicar mensagem no SNS: erro desconhecido',
        );
      }
      throw error;
    }
  }
}
