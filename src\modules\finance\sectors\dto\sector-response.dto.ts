import { ApiProperty } from '@nestjs/swagger';

export class SectorResponseDto {
  @ApiProperty({
    description: 'Unique identifier (UUID) of the sector',
    example: 'b5d9e3f1-8f3a-4c2d-9f2b-abcdef123456',
  })
  uuid: string;

  @ApiProperty({
    description: 'Unique code of the sector',
    example: 'HR',
  })
  code: string;

  @ApiProperty({
    description: 'Description of the sector',
    example: 'Human Resources',
  })
  description: string;

  @ApiProperty({
    description: 'UUID of the user who created the sector',
    example: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
  })
  createdBy: string;

  @ApiProperty({
    description: 'UUID of the user who last updated the sector',
    example: 'a3f47d2e-1c2b-4f7a-9d2e-1234567890ab',
  })
  updatedBy: string;

  @ApiProperty({
    description: 'Creation timestamp in ISO format',
    example: '2025-05-07T00:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp in ISO format',
    example: '2025-05-07T00:00:00Z',
  })
  updatedAt: string;
}
