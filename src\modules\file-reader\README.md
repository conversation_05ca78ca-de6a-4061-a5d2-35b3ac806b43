# FileReader Module

Um módulo NestJS reutilizável para leitura e parsing de arquivos enviados via multipart/form-data, com suporte a CSV e XLSX.

## Características

- ✅ Suporte a arquivos CSV e XLSX
- ✅ Leitura usando Streams para escalabilidade
- ✅ Validação de colunas obrigatórias
- ✅ Tratamento de erros robusto
- ✅ Configurações flexíveis (delimitador, trim, skip empty lines)
- ✅ Totalmente desacoplado da lógica de negócio
- ✅ TypeScript com tipagem completa
- ✅ Testes unitários incluídos

## Instalação

```bash
npm install csv-parser xlsx papaparse
npm install --save-dev @types/multer
```

## Uso Básico

### 1. Importar o módulo

```typescript
import { Module } from '@nestjs/common';
import { FileReaderModule } from './modules/file-reader';

@Module({
  imports: [FileReaderModule],
  // ...
})
export class YourModule {}
```

### 2. Usar o serviço

```typescript
import { Injectable } from '@nestjs/common';
import { FileReaderService } from './modules/file-reader';

@Injectable()
export class YourService {
  constructor(private readonly fileReaderService: FileReaderService) {}

  async processFile(file: Express.Multer.File) {
    const result = await this.fileReaderService.readFile(file, {
      requiredColumns: ['name', 'email'],
      trimValues: true,
      skipEmptyLines: true,
    });

    console.log(`Processed ${result.totalRows} rows`);
    console.log('Columns:', result.columns);
    console.log('Data:', result.data);
    
    return result;
  }
}
```

### 3. Controlador com upload

```typescript
import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileReaderService } from './modules/file-reader';

@Controller('upload')
export class UploadController {
  constructor(private readonly fileReaderService: FileReaderService) {}

  @Post('csv')
  @UseInterceptors(FileInterceptor('file'))
  async uploadCsv(@UploadedFile() file: Express.Multer.File) {
    return await this.fileReaderService.readFile(file, {
      requiredColumns: ['name', 'email', 'phone'],
      delimiter: ',',
    });
  }

  @Post('excel')
  @UseInterceptors(FileInterceptor('file'))
  async uploadExcel(@UploadedFile() file: Express.Multer.File) {
    return await this.fileReaderService.readFile(file);
  }
}
```

## API Reference

### FileReaderService

#### `readFile(file: Express.Multer.File, options?: FileReaderOptions): Promise<ParsedFileResult>`

Lê e faz parsing de um arquivo.

**Parâmetros:**
- `file`: Arquivo enviado via multer
- `options`: Opções de configuração (opcional)

**Retorna:** `ParsedFileResult` com os dados parseados

#### `getFileInfo(file: Express.Multer.File): Promise<FileInfo>`

Obtém informações do arquivo sem fazer parsing completo.

### Interfaces

#### `FileReaderOptions`

```typescript
interface FileReaderOptions {
  requiredColumns?: string[];    // Colunas obrigatórias
  delimiter?: string;           // Delimitador CSV (padrão: ',')
  encoding?: BufferEncoding;    // Encoding do arquivo
  skipEmptyLines?: boolean;     // Pular linhas vazias (padrão: true)
  trimValues?: boolean;         // Remover espaços (padrão: true)
}
```

#### `ParsedFileResult`

```typescript
interface ParsedFileResult {
  data: Record<string, any>[];  // Dados parseados
  totalRows: number;            // Total de linhas
  columns: string[];            // Nomes das colunas
  errors?: string[];            // Erros encontrados
}
```

## Tratamento de Erros

O módulo inclui exceções específicas:

- `UnsupportedFileTypeException`: Tipo de arquivo não suportado
- `FileCorruptedException`: Arquivo corrompido ou inválido
- `MissingRequiredColumnsException`: Colunas obrigatórias ausentes
- `FileProcessingException`: Erro genérico de processamento

## Exemplos Avançados

### Validação de Colunas

```typescript
// Verificar se arquivo tem colunas necessárias antes do processamento
const fileInfo = await this.fileReaderService.getFileInfo(file);
const requiredColumns = ['name', 'email', 'phone'];
const hasAllColumns = requiredColumns.every(col => 
  fileInfo.columns?.includes(col)
);

if (!hasAllColumns) {
  throw new BadRequestException('Arquivo não possui todas as colunas necessárias');
}
```

### Processamento com Validação Customizada

```typescript
async importCustomers(file: Express.Multer.File) {
  const result = await this.fileReaderService.readFile(file, {
    requiredColumns: ['name', 'email', 'phone'],
    trimValues: true,
  });

  const validCustomers = [];
  const errors = [];

  result.data.forEach((row, index) => {
    try {
      // Validação customizada
      if (!row.email.includes('@')) {
        throw new Error(`Email inválido: ${row.email}`);
      }
      
      if (row.phone.length < 10) {
        throw new Error(`Telefone inválido: ${row.phone}`);
      }

      validCustomers.push({
        name: row.name,
        email: row.email.toLowerCase(),
        phone: row.phone.replace(/\D/g, ''), // Apenas números
      });
    } catch (error) {
      errors.push(`Linha ${index + 2}: ${error.message}`);
    }
  });

  return {
    imported: validCustomers.length,
    errors,
    data: validCustomers,
  };
}
```

### Configuração de Multer

```typescript
import { MulterModule } from '@nestjs/platform-express';

@Module({
  imports: [
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(csv|xlsx)$/)) {
          return callback(new Error('Apenas arquivos CSV e XLSX são permitidos'), false);
        }
        callback(null, true);
      },
    }),
    FileReaderModule,
  ],
})
export class UploadModule {}
```

## Testes

Execute os testes unitários:

```bash
npm run test src/modules/file-reader
```

## Estrutura do Módulo

```
src/modules/file-reader/
├── exceptions/
│   └── file-reader.exceptions.ts
├── interfaces/
│   └── file-reader.interface.ts
├── services/
│   ├── file-reader.service.ts
│   └── file-reader.service.spec.ts
├── examples/
│   ├── file-upload.controller.ts
│   └── example.module.ts
├── file-reader.module.ts
├── index.ts
└── README.md
```

## Contribuição

Para contribuir com melhorias:

1. Adicione testes para novas funcionalidades
2. Mantenha a compatibilidade com a interface existente
3. Documente mudanças no README
4. Siga os padrões de código do projeto
