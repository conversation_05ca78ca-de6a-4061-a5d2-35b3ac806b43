import { Injectable, Inject } from '@nestjs/common';
import { SupplierContact } from '../../../domain/supplier/entities/supplier-contact.entity';
import { SupplierContactRepositoryPort } from '../../../ports/repositories/supplier-contact-repository.port';

@Injectable()
export class DeleteSupplierContactUseCase {
  constructor(
    @Inject('SupplierContactRepository')
    private readonly supplierContactRepository: SupplierContactRepositoryPort,
  ) { }

  async execute(uuid: string): Promise<void> {
    const contact = await this.supplierContactRepository.delete(uuid);
    return contact;
  }
} 