import { ApiProperty } from '@nestjs/swagger';
import { DomainResponseDto } from './domain-response.dto';

export class DomainListDto {
  @ApiProperty({ type: [DomainResponseDto], description: 'Lista de domínios' })
  items: DomainResponseDto[];

  @ApiProperty({ description: 'Número de itens por página' })
  limit: number;

  @ApiProperty({ description: 'Número de itens pulados' })
  offset: number;

  @ApiProperty({ description: 'Total de itens' })
  total: number;
} 