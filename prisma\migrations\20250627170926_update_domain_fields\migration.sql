/*
  Warnings:

  - You are about to drop the column `type` on the `domains` table. All the data in the column will be lost.
  - Added the required column `brandName` to the `domains` table without a default value. This is not possible if the table is not empty.
  - Added the required column `licenseNumber` to the `domains` table without a default value. This is not possible if the table is not empty.
  - Added the required column `licenseType` to the `domains` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "core"."LicenseType" AS ENUM ('FEDERAL', 'ESTADUAL');

-- AlterTable
ALTER TABLE "core"."domains" DROP COLUMN "type",
ADD COLUMN     "brandName" TEXT NOT NULL,
ADD COLUMN     "licenseNumber" TEXT NOT NULL,
ADD COLUMN     "licenseType" "core"."LicenseType" NOT NULL,
ADD COLUMN     "notes" TEXT;

-- DropEnum
DROP TYPE "core"."DomainType";
