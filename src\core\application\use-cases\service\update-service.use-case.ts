import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { ServiceRepositoryPort } from '../../../ports/repositories/service-repository.port';
import { Service } from '../../../domain/service/entities/service.entity';
import { UpdateServiceDto } from '../../../../modules/finance/dto/update-service.dto';

@Injectable()
export class UpdateServiceUseCase {
  constructor(
    @Inject('SERVICE_REPOSITORY')
    private readonly serviceRepository: ServiceRepositoryPort,
  ) {}

  async execute(id: string, dto: UpdateServiceDto, updatedBy: string): Promise<Service> {
    const service = await this.serviceRepository.findById(id);
    
    if (!service) {
      throw new NotFoundException(`Serviço com ID ${id} não encontrado`);
    }

    if (dto.type) {
      service.updateType(dto.type, updatedBy);
    }

    if (dto.rate) {
      service.updateRate(dto.rate, updatedBy);
    }

    if (dto.description) {
      service.updateDescription(dto.description, updatedBy);
    }

    return await this.serviceRepository.update(service);
  }
} 