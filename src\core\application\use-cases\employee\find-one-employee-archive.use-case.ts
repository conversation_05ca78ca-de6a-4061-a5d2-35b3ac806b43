import { ARCHIVE_REPOSITORY, ArchiveRepositoryPort } from "@/core/ports/repositories/archive-repository.port";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { ArchiveWithUrl } from "./find-employee-archive-by-uuid.use-case";

@Injectable()
export class FindOneEmployeeArchiveUseCase {
  constructor(
    @Inject(ARCHIVE_REPOSITORY)
    private readonly archiveRepository: ArchiveRepositoryPort,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(id: string, expiresIn: number = 3600): Promise<ArchiveWithUrl> {
    const archive = await this.archiveRepository.findOne(id);
    if (!archive) {
      throw new NotFoundException('Arquivo não encontrado.');
    }
    return {
      ...archive,
      downloadUrl: await this.storageProvider.getDownloadUrl(archive.filePath, archive.fileName || 'document.pdf', expiresIn),
    };
  }
}