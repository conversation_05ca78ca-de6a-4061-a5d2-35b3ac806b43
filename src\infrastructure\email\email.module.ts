import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailService } from './nodemailer-email.service.interface';
import { SESEmailService } from './ses-email.service';
import { AwsModule } from '../aws/aws.module';

@Module({
  imports: [ConfigModule, AwsModule],
  providers: [
    {
      provide: 'EmailService',
      useFactory: (configService: ConfigService, sesEmailService: SESEmailService) => {
        const emailProvider = configService.get<string>('EMAIL_PROVIDER', 'ses');
        
        switch (emailProvider.toLowerCase()) {
          case 'ses':
            return sesEmailService;
          default:
            console.warn(`Provedor de email desconhecido: ${emailProvider}. Usando SES como padrão.`);
            return sesEmailService;
        }
      },
      inject: [ConfigService, SESEmailService],
    },
    SESEmailService,
  ],
  exports: ['EmailService'],
})
export class EmailModule {} 