import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { ICustomerDocumentRepository } from '../../domain/repositories/customer-document.repository.interface';
import { CUSTOMER_DOCUMENT_REPOSITORY } from '../../domain/constants/tokens';

@Injectable()
export class DeleteCustomerDocumentUseCase {
  constructor(
    @Inject(CUSTOMER_DOCUMENT_REPOSITORY)
    private readonly customerDocumentRepository: ICustomerDocumentRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const document = await this.customerDocumentRepository.findById(id);

    if (!document) {
      throw new NotFoundException(`Document with id ${id} not found`);
    }

    await this.customerDocumentRepository.delete(id);
  }
}
