import { Injectable, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  PayablesTypeRepositoryPort,
  PAYABLES_TYPE_REPOSITORY,
} from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';
import { CreatePayablesTypeDto } from '@modules/finance/dto/create-payables-type.dto';
import { ConflictException } from '@nestjs/common';

@Injectable()
export class CreatePayablesTypeUseCase {
  constructor(
    @Inject(PAYABLES_TYPE_REPOSITORY)
    private readonly payablesTypeRepository: PayablesTypeRepositoryPort,
  ) {}

  async execute(dto: CreatePayablesTypeDto): Promise<PayablesType> {
    const existingPayablesType = await this.payablesTypeRepository.findByCode(
      dto.code,
    );
    if (existingPayablesType) {
      throw new ConflictException({
        code: 'Código já existente',
        message: 'O código do tipo de despesa já existe',
      });
    }

    const payablesType = new PayablesType(
      uuidv4(),
      dto.code,
      dto.description,
      dto.createdBy,
      dto.updatedBy,
      new Date(),
      new Date(),
    );

    return this.payablesTypeRepository.create(payablesType);
  }
}
