import { Injectable } from '@nestjs/common';
import { AuditEvent } from './audit.interface';
import { RabbitMQService } from '@/infrastructure/messaging/rabbitmq/rabbitmq.service';

@Injectable()
export class AuditPublisherService {
  private readonly EXCHANGE = 'audit.events';
  private readonly QUEUE = 'audit.log.queue';
  private readonly ROUTING_KEY = 'audit.action';

  constructor(private readonly rabbitMQService: RabbitMQService) {
    void this.setupQueuesAndBindings();
  }

  private async setupQueuesAndBindings() {
    // Configurar filas para eventos de auditoria
    await this.rabbitMQService.bindQueue(
      this.QUEUE,
      this.EXCHANGE,
      this.ROUTING_KEY,
    );
  }

  async publish(event: AuditEvent): Promise<void> {
    // Publica o evento de auditoria no RabbitMQ
    await this.rabbitMQService.publish(
      this.EXCHANGE,
      this.ROUTING_KEY,
      {
        ...event,
      },
      {
        persistent: true,
        contentType: 'application/json',
      },
    );
    console.log('Audit Event Published to RabbitMQ:', event);
  }
}
