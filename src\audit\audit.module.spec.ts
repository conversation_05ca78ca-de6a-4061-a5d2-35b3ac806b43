import { Test, TestingModule } from '@nestjs/testing';
import { AuditPublisherService } from './audit-publisher.service';
import { AuditInterceptor } from './audit.interceptor';
import { RabbitMQService } from '@/infrastructure/messaging/rabbitmq/rabbitmq.service';

describe('AuditModule', () => {
  let module: TestingModule;

  const mockRabbitMQService = {
    bindQueue: jest.fn(),
    publish: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        AuditPublisherService,

        AuditInterceptor,
        {
          provide: RabbitMQService,
          useValue: mockRabbitMQService,
        },
      ],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide AuditPublisherService', () => {
    const auditPublisherService = module.get<AuditPublisherService>(
      AuditPublisherService,
    );
    expect(auditPublisherService).toBeDefined();
    expect(auditPublisherService).toBeInstanceOf(AuditPublisherService);
  });

  it('should provide AuditInterceptor', () => {
    const auditInterceptor = module.get<AuditInterceptor>(AuditInterceptor);
    expect(auditInterceptor).toBeDefined();
    expect(auditInterceptor).toBeInstanceOf(AuditInterceptor);
  });

  describe('Module Integration', () => {
    it('should inject RabbitMQService into AuditPublisherService', () => {
      const auditPublisherService = module.get<AuditPublisherService>(
        AuditPublisherService,
      );
      expect(auditPublisherService).toBeDefined();

      // Verify that the service was created successfully with the mocked dependency
      expect(mockRabbitMQService.bindQueue).toHaveBeenCalled();
    });

    it('should inject AuditPublisherService into AuditInterceptor', () => {
      const auditPublisherService = module.get<AuditPublisherService>(
        AuditPublisherService,
      );
      const interceptor = module.get<AuditInterceptor>(AuditInterceptor);

      expect(auditPublisherService).toBeDefined();
      expect(interceptor).toBeDefined();

      // Both should be instances of their respective classes
      expect(auditPublisherService).toBeInstanceOf(AuditPublisherService);
      expect(interceptor).toBeInstanceOf(AuditInterceptor);
    });

    it('should verify all providers are properly configured', () => {
      // This test ensures that all the required providers exist and can be resolved
      const providers = [
        AuditPublisherService,
        AuditInterceptor,
        RabbitMQService,
      ];

      providers.forEach((provider) => {
        const instance = module.get(provider);
        expect(instance).toBeDefined();
      });
    });
  });
});
