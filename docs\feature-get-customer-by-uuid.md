# Feature: Consulta de Cliente por UUID (BACKOFFICE-176)

## Descrição
Como Administrador Financeiro, eu quero obter detalhes de um cliente via UUID para que eu valide os dados antes de alterações.

## Status da Implementação
✅ **Implementado com sucesso**

## Critérios de Aceite

| Critério | Status | Detalhes |
|----------|--------|----------|
| UUID inválido → 400 Bad Request | ✅ | Implementado usando class-validator e DTO específico (CustomerUuidParamDto) |
| Cliente não encontrado → 404 Not Found | ✅ | Implementado com tratamento de exceção NotFoundException |
| Sucesso → 200 OK + Customer | ✅ | Retorno dos dados do cliente com HttpCode(HttpStatus.OK) |

## Implementação Técnica

### Arquitetura
A implementação seguiu o padrão Clean Architecture conforme especificado no projeto:

- **Domínio**: 
  - Interface `src/modules/customers/domain/repositories/customer.repository.interface.ts`
  - Interface `src/modules/customers/domain/use-cases/find-customer-by-uuid.use-case.interface.ts`
  - Entidade `src/modules/customers/domain/entities/customer.entity.ts`

- **Aplicação**: 
  - Use case `src/modules/customers/application/use-cases/find-customer-by-uuid.use-case.ts`
  - Controller `src/modules/customers/application/controllers/customer.controller.ts`

- **Infraestrutura**: 
  - DTO `src/modules/customers/infrastructure/dtos/customer-uuid.dto.ts`
  - Repository `src/modules/customers/infrastructure/repositories/customer.repository.ts`

### Componentes Implementados

1. **Controller**:
   ```typescript
   @ApiTags('Customers')
   @Controller('customers')
   export class CustomerController {
     constructor(
       private readonly findCustomerByUuidUseCase: FindCustomerByUuidUseCase,
     ) {}

     @Get(':uuid')
     @HttpCode(HttpStatus.OK)
     @ApiOperation({ summary: 'Get customer by UUID' })
     @ApiParam({
       name: 'uuid',
       description: 'Customer UUID',
       type: String,
       format: 'uuid',
       example: '123e4567-e89b-12d3-a456-************',
     })
     @ApiResponse({
       status: HttpStatus.OK,
       description: 'Customer found successfully',
     })
     @ApiResponse({
       status: HttpStatus.BAD_REQUEST,
       description: 'Invalid UUID format',
     })
     @ApiResponse({
       status: HttpStatus.NOT_FOUND,
       description: 'Customer not found',
     })
     async findByUuid(@Param() params: CustomerUuidParamDto) {
       return this.findCustomerByUuidUseCase.execute(params.uuid);
     }
   }
   ```

2. **DTO para validação**:
   ```typescript
   export class CustomerUuidParamDto {
     @IsUUID(4)
     @ApiProperty({
       description: 'Customer UUID',
       example: '123e4567-e89b-12d3-a456-************',
     })
     uuid: string;
   }
   ```

3. **Use Case**:
   ```typescript
   @Injectable()
   export class FindCustomerByUuidUseCase implements IFindCustomerByUuidUseCase {
     constructor(
       @Inject('CUSTOMER_REPOSITORY')
       private readonly customerRepository: ICustomerRepository,
     ) {}

     async execute(uuid: string): Promise<Customer> {
       const customer = await this.customerRepository.findByUuid(uuid);
       
       if (!customer) {
         throw new NotFoundException(`Customer with UUID ${uuid} not found`);
       }
       
       return customer;
     }
   }
   ```

4. **Repository**:
   ```typescript
   @Injectable()
   export class CustomerRepository implements ICustomerRepository {
     constructor(private readonly prisma: PrismaService) {}

     async findByUuid(uuid: string): Promise<Customer | null> {
       const customer = await this.prisma.core_customers.findUnique({
         where: { uuid },
       });

       if (!customer) {
         return null;
       }

       return {
         id: customer.id,
         uuid: customer.uuid,
         name: customer.name,
         // ... outros campos mapeados
       };
     }
   }
   ```

### Swagger/OpenAPI

A documentação Swagger foi atualizada seguindo o padrão estabelecido no projeto, com todos os endpoints em inglês para operações:

- Tag: `@ApiTags('Customers')`
- Operação: `@ApiOperation({ summary: 'Get customer by UUID' })`
- Parâmetros: Documentados com `@ApiParam`
- Respostas: Todas as respostas possíveis documentadas com `@ApiResponse`

### Testes

Implementamos os seguintes testes:

- ✅ Teste unitário do controller (`src/modules/customers/__tests__/unit/customer.controller.spec.ts`)
- ✅ Teste unitário do use case (`src/modules/customers/__tests__/unit/find-customer-by-uuid.use-case.spec.ts`) 
- ✅ Teste unitário do repositório (`src/modules/customers/__tests__/unit/customer.repository.spec.ts`)

### Módulo

O módulo foi registrado corretamente:

```typescript
@Module({
  controllers: [CustomerController],
  providers: [
    FindCustomerByUuidUseCase,
    {
      provide: 'CUSTOMER_REPOSITORY',
      useClass: CustomerRepository,
    },
  ],
})
export class CustomersModule {}
```

## Melhorias na Interface Swagger

Como parte da tarefa, também padronizamos a nomenclatura em inglês para todos os endpoints do Swagger na aplicação:

1. Alteramos todas as tags para inglês: Companies, Users, Payment Methods, Authentication, etc.
2. Alteramos todos os summaries de operações para inglês
3. Mantivemos as descrições de respostas em português conforme solicitado

## Conclusão

A feature foi implementada com sucesso segundo os princípios de Clean Architecture. O endpoint `/customers/{uuid}` agora está disponível para consulta de clientes via UUID, com tratamento apropriado para todos os cenários de erro e documentação Swagger atualizada.

A implementação atende a todos os critérios de aceite e está pronta para uso pelos administradores financeiros para validação de dados antes de alterações. 