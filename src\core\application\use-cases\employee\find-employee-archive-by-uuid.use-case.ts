import { ARCHIVE_REPOSITORY, ArchiveRepositoryPort } from "@/core/ports/repositories/archive-repository.port";
import { IStorageProvider } from "@/core/ports/storage/storage-provider.port";
import { Inject, Injectable } from "@nestjs/common";
import { Archive } from "@prisma/client";

export interface ArchiveWithUrl extends Archive {
  downloadUrl?: string;
}

@Injectable()
export class FindEmployeeArchiveByUuidUseCase {
  constructor(
    @Inject(ARCHIVE_REPOSITORY)
    private readonly archiveRepository: ArchiveRepositoryPort,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(employeeUuid: string, expiresIn: number = 3600): Promise<ArchiveWithUrl[]> {
    const archives = await this.archiveRepository.findByEmployeeUuid(employeeUuid);

    // Gerar URLs de download para todos os arquivos
    const archivesWithUrls = await Promise.all(
      archives.map(async (archive) => {
        try {
          // Verificar se o arquivo tem path antes de tentar gerar URL
          if (archive.filePath && archive.filePath.trim() !== '') {
            const downloadUrl = await this.storageProvider.getDownloadUrl(
              archive.filePath,
              archive.fileName || 'document.pdf',
              expiresIn
            );
            return {
              ...archive,
              downloadUrl,
            };
          } else {
            // Arquivo sem path - retorna sem URL
            return {
              ...archive,
              downloadUrl: undefined,
            };
          }
        } catch (error) {
          console.error(`Erro ao gerar URL de download para arquivo ${archive.id}:`, error);
          // Retorna sem URL em caso de erro
          return {
            ...archive,
            downloadUrl: undefined,
          };
        }
      })
    );

    return archivesWithUrls;
  }
}