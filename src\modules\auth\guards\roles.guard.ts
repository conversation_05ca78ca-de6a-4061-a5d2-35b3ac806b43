import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';

interface KeycloakUser {
  azp: string;
  sub: string;
  username: string;
  email: string;
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [key: string]: {
      roles: string[];
    };
  };
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredRoles) {
      return true;
    }

    const request = context
      .switchToHttp()
      .getRequest<Request & { user: KeycloakUser }>();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('<PERSON>u<PERSON><PERSON> não autenticado');
    }

    const realmRoles = user.realm_access?.roles || [];
    const resourceRoles = user.resource_access?.[user.azp]?.roles || [];

    const hasRole = requiredRoles.some((role) => {
      return realmRoles.includes(role) || resourceRoles.includes(role);
    });

    if (!hasRole) {
      console.warn(
        `Usuário ${user.username} não tem permissão para acessar este recurso. Requeridos: ${requiredRoles.join(
          ', ',
        )}, Disponíveis: ${[...realmRoles, ...resourceRoles].join(', ')}`,
      );
      throw new ForbiddenException(
        'Usuário não tem permissão para acessar este recurso',
      );
    }

    return true;
  }
}
