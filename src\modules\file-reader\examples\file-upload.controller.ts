import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FileReaderService } from '../services/file-reader.service';
import { FileReaderOptions, ParsedFileResult } from '../interfaces/file-reader.interface';

class FileUploadDto {
  requiredColumns?: string[];
  delimiter?: string;
  skipEmptyLines?: boolean;
  trimValues?: boolean;
}

@ApiTags('File Upload Examples')
@Controller('examples/file-upload')
export class FileUploadController {
  constructor(private readonly fileReaderService: FileReaderService) {}

  @Post('csv')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload and parse CSV file' })
  @ApiResponse({ status: 200, description: 'File parsed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file or missing required columns' })
  async uploadCsv(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: FileUploadDto = {},
  ): Promise<ParsedFileResult> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const fileReaderOptions: FileReaderOptions = {
      requiredColumns: options.requiredColumns,
      delimiter: options.delimiter || ',',
      skipEmptyLines: options.skipEmptyLines !== false,
      trimValues: options.trimValues !== false,
    };

    return await this.fileReaderService.readFile(file, fileReaderOptions);
  }

  @Post('xlsx')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload and parse Excel file' })
  @ApiResponse({ status: 200, description: 'File parsed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file or missing required columns' })
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: FileUploadDto = {},
  ): Promise<ParsedFileResult> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const fileReaderOptions: FileReaderOptions = {
      requiredColumns: options.requiredColumns,
      skipEmptyLines: options.skipEmptyLines !== false,
      trimValues: options.trimValues !== false,
    };

    return await this.fileReaderService.readFile(file, fileReaderOptions);
  }

  @Post('customers-import')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import customers from CSV/Excel file' })
  @ApiResponse({ status: 200, description: 'Customers imported successfully' })
  async importCustomers(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<{
    imported: number;
    errors: string[];
    preview: any[];
  }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Define required columns for customer import
    const requiredColumns = ['name', 'email', 'phone'];
    
    const result = await this.fileReaderService.readFile(file, {
      requiredColumns,
      trimValues: true,
      skipEmptyLines: true,
    });

    // Process the data (example business logic)
    const processedCustomers = result.data.map((row, index) => {
      const customer = {
        name: row.name || row.Name || row.NAME,
        email: row.email || row.Email || row.EMAIL,
        phone: row.phone || row.Phone || row.PHONE,
        rowNumber: index + 1,
      };

      // Basic validation
      if (!customer.email || !customer.email.includes('@')) {
        throw new BadRequestException(
          `Invalid email at row ${customer.rowNumber}: ${customer.email}`
        );
      }

      return customer;
    });

    return {
      imported: processedCustomers.length,
      errors: result.errors || [],
      preview: processedCustomers.slice(0, 5), // Show first 5 records
    };
  }

  @Post('file-info')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Get file information without parsing entire content' })
  @ApiResponse({ status: 200, description: 'File information retrieved' })
  async getFileInfo(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<{
    filename: string;
    size: number;
    type: string;
    columns?: string[];
  }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return await this.fileReaderService.getFileInfo(file);
  }

  @Post('validate-columns')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Validate if file has required columns' })
  @ApiResponse({ status: 200, description: 'Column validation result' })
  async validateColumns(
    @UploadedFile() file: Express.Multer.File,
    @Body('requiredColumns') requiredColumns: string[],
  ): Promise<{
    isValid: boolean;
    fileColumns: string[];
    requiredColumns: string[];
    missingColumns: string[];
  }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!requiredColumns || !Array.isArray(requiredColumns)) {
      throw new BadRequestException('requiredColumns must be an array of strings');
    }

    const fileInfo = await this.fileReaderService.getFileInfo(file);
    const fileColumns = fileInfo.columns || [];
    
    const missingColumns = requiredColumns.filter(
      required => !fileColumns.some(col => 
        col.toLowerCase().trim() === required.toLowerCase().trim()
      )
    );

    return {
      isValid: missingColumns.length === 0,
      fileColumns,
      requiredColumns,
      missingColumns,
    };
  }
}
