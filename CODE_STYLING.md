# Guia de Estilo de Código

Este documento serve como um guia para as convenções de estilo de código e melhores práticas adotadas neste projeto. Seguindo estas diretrizes, mantemos a consistência e legibilidade do código em toda a base de código.

## Tabela de Conteúdos

- [Ferramentas e Configurações](#ferramentas-e-configurações)
- [Formatação e Estilo](#formatação-e-estilo)
- [TypeScript](#typescript)
- [Princípios de Engenharia de Software](#princípios-de-engenharia-de-software)
- [Estrutura do Projeto](#estrutura-do-projeto)
- [Nomenclatura](#nomenclatura)
- [Comentários e Documentação](#comentários-e-documentação)
- [Testes](#testes)
- [Git e Commits](#git-e-commits)

## Ferramentas e Configurações

O projeto utiliza as seguintes ferramentas para garantir a qualidade e consistência do código:

- **ESLint**: Para verificação de código estático
- **Prettier**: Para formatação de código
- **TypeScript**: Para tipagem estática
- **Husky**: Para hooks de git pré-commit
- **Lint-staged**: Para executar linters apenas em arquivos em staging

Certifique-se de ter as extensões corretas instaladas em seu editor e que ele esteja configurado para usar estas ferramentas.

## Formatação e Estilo

### Regras Gerais

- Indentação de 2 espaços
- Aspas simples (`'`) para strings
- Vírgula no final de todos os itens em listas multilinha
- Comprimento máximo de linha de 100 caracteres
- Quebras de linha no estilo LF (Unix)

### Prettier

O projeto usa Prettier com as seguintes configurações:

```json
{
  "singleQuote": true,
  "trailingComma": "all"
}
```

## TypeScript

### Configurações e Boas Práticas

- Utilize tipagem estrita sempre que possível
- Use `strictNullChecks` para evitar erros de null/undefined
- Evite o uso de `any` (está configurado como erro no ESLint)
- Use interfaces para definir contratos e tipos para aliases
- Tire proveito dos decoradores do NestJS
- Configure `noImplicitAny` sempre que possível

### ESLint

O projeto segue as seguintes regras de ESLint:

- `@typescript-eslint/no-explicit-any`: Erro
- `@typescript-eslint/no-floating-promises`: Aviso
- `@typescript-eslint/no-unsafe-argument`: Erro
- `@typescript-eslint/no-base-to-string`: Aviso
- `@typescript-eslint/restrict-template-expressions`: Aviso
- `@typescript-eslint/no-unsafe-assignment`: Erro
- `@typescript-eslint/no-unsafe-call`: Aviso
- `@typescript-eslint/no-unused-vars`: Erro (ignorando variáveis prefixadas com `_`)

## Princípios de Engenharia de Software

Aplicamos princípios fundamentais de engenharia de software, sempre com equilíbrio e bom senso, adaptando-os às necessidades específicas do projeto.

### SOLID

#### Princípio da Responsabilidade Única (SRP)
- Uma classe deve ter apenas um motivo para mudar
- **Exemplo Equilibrado**: 
  ```typescript
  // Bom: Classe focada em uma responsabilidade
  class UserAuthentication {
    validateCredentials(username: string, password: string): boolean {
      // Lógica de validação
    }
    
    generateToken(userId: string): string {
      // Lógica de geração de token
    }
  }
  
  // Evite: Classe com múltiplas responsabilidades não relacionadas
  class UserService {
    validateCredentials() { /* ... */ }
    generateToken() { /* ... */ }
    updateUserProfile() { /* ... */ }
    calculateUserStatistics() { /* ... */ }
    sendWelcomeEmail() { /* ... */ }
  }
  ```
- **Aplicação**: Uma classe pode ter métodos relacionados (como autenticação e geração de token), desde que pertençam ao mesmo domínio de responsabilidade.

#### Princípio do Aberto/Fechado (OCP)
- Entidades devem estar abertas para extensão, mas fechadas para modificação
- **Exemplo Equilibrado**:
  ```typescript
  // Interface base
  interface NotificationChannel {
    send(message: string, recipient: string): Promise<void>;
  }
  
  // Implementações específicas
  class EmailNotification implements NotificationChannel {
    async send(message: string, email: string): Promise<void> {
      // Implementação para email
    }
  }
  
  class SMSNotification implements NotificationChannel {
    async send(message: string, phoneNumber: string): Promise<void> {
      // Implementação para SMS
    }
  }
  
  // Serviço que utiliza o canal de notificação
  class NotificationService {
    constructor(private channel: NotificationChannel) {}
    
    async notify(message: string, recipient: string): Promise<void> {
      await this.channel.send(message, recipient);
    }
  }
  ```
- **Aplicação**: Nem todas as classes precisam seguir esse princípio. É mais valioso para componentes centrais ou que provavelmente precisarão de extensão.

#### Princípio da Substituição de Liskov (LSP)
- Subtipos devem ser substituíveis por seus tipos base
- **Exemplo Equilibrado**:
  ```typescript
  // Classe base
  class Repository<T> {
    async findAll(): Promise<T[]> {
      // Implementação base
      return [];
    }
  }
  
  // Subclasse que respeita o princípio
  class UserRepository extends Repository<User> {
    async findAll(): Promise<User[]> {
      // Implementação específica para usuários
      // Deve comportar-se de maneira compatível com a classe base
      return await this.query('SELECT * FROM users');
    }
    
    // Métodos adicionais são permitidos
    async findByEmail(email: string): Promise<User | null> {
      // ...
    }
  }
  ```
- **Aplicação**: Certifique-se de que as classes derivadas não violem as expectativas da classe base, mas não se preocupe com herança perfeita em todos os casos.

#### Princípio da Segregação de Interface (ISP)
- Clientes não devem ser forçados a depender de interfaces que não utilizam
- **Exemplo Equilibrado**:
  ```typescript
  // Interfaces segregadas
  interface Readable {
    read(): string;
  }
  
  interface Writable {
    write(data: string): void;
  }
  
  // Classes implementam apenas o que precisam
  class FileReader implements Readable {
    read(): string {
      return 'file contents';
    }
  }
  
  class FileWriter implements Writable {
    write(data: string): void {
      // Escreve dados no arquivo
    }
  }
  
  class FileManager implements Readable, Writable {
    read(): string {
      return 'file contents';
    }
    
    write(data: string): void {
      // Escreve dados no arquivo
    }
  }
  ```
- **Aplicação**: Interfaces menores são melhores, mas não exagere criando interfaces para cada método.

#### Princípio da Inversão de Dependência (DIP)
- Módulos de alto nível não devem depender de módulos de baixo nível. Ambos devem depender de abstrações.
- **Exemplo Equilibrado**:
  ```typescript
  // Abstração
  interface Logger {
    log(message: string): void;
  }
  
  // Implementação de baixo nível
  class ConsoleLogger implements Logger {
    log(message: string): void {
      console.log(message);
    }
  }
  
  // Módulo de alto nível usando a abstração
  class UserService {
    constructor(private logger: Logger) {}
    
    createUser(userData: any): void {
      // Lógica para criar usuário
      this.logger.log('Usuário criado com sucesso');
    }
  }
  ```
- **Aplicação**: Use injeção de dependência para componentes principais, mas não é necessário abstrair absolutamente tudo.

### DRY (Don't Repeat Yourself)

O princípio DRY visa reduzir a repetição de código, mas deve ser aplicado com equilíbrio.

- **Exemplo Equilibrado**:
  ```typescript
  // Bom: Função utilitária para validação comum
  function isValidEmail(email: string): boolean {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  }
  
  // Uso em múltiplos lugares
  class UserValidator {
    validateUserInput(user: any): boolean {
      return isValidEmail(user.email);
    }
  }
  
  class EmailService {
    sendEmail(email: string, content: string): void {
      if (!isValidEmail(email)) {
        throw new Error('Email inválido');
      }
      // Enviar email
    }
  }
  ```

- **Aplicação**: 
  - Extraia código duplicado em funções utilitárias quando ele aparecer em 3 ou mais lugares
  - Às vezes, um pouco de duplicação é melhor que uma abstração errada
  - Considere a "Regra de Três": Espere até que algo seja duplicado três vezes antes de refatorar

### Clean Code

Práticas de código limpo que promovem legibilidade e manutenibilidade.

#### Funções

- **Exemplo Equilibrado**:
  ```typescript
  // Bom: Função com único propósito e nomeação clara
  function authenticateUser(username: string, password: string): User | null {
    const user = findUserByUsername(username);
    if (!user) return null;
    
    const isPasswordValid = validatePassword(password, user.passwordHash);
    if (!isPasswordValid) return null;
    
    return user;
  }
  
  // Evite: Função grande que faz muitas coisas
  function processUser(username: string, password: string, data: any): void {
    // 100 linhas de código misturando autenticação, 
    // atualização de perfil, logging, envio de email...
  }
  ```

- **Aplicação**:
  - Funções devem fazer uma coisa, mas "uma coisa" depende do contexto
  - Prefira funções menores (5-15 linhas), mas não quebre funções coesas apenas para reduzir o tamanho
  - Use nomes descritivos que revelam a intenção

#### Variáveis e Nomes

- **Exemplo Equilibrado**:
  ```typescript
  // Bom: Nomes significativos
  const activeUsers = users.filter(user => user.isActive);
  const paymentDueDate = new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000);
  
  // Melhor: Extraindo constantes significativas
  const THIRTY_DAYS_IN_MS = 30 * 24 * 60 * 60 * 1000;
  const paymentDueDate = new Date(currentDate.getTime() + THIRTY_DAYS_IN_MS);
  ```

- **Aplicação**:
  - Use nomes significativos para variáveis e funções
  - Nomes mais longos para escopos mais amplos
  - Nomes curtos são aceitáveis para escopos muito limitados (como índices de loop)

#### Comentários

- **Exemplo Equilibrado**:
  ```typescript
  // Comentário desnecessário
  // Incrementa contador
  counter++;
  
  // Comentário útil explicando "por quê", não "o quê"
  // O timeout de 5 segundos é necessário para contornar 
  // uma limitação da API externa que limita requisições
  await new Promise(resolve => setTimeout(resolve, 5000));
  ```

- **Aplicação**:
  - Prefira código autoexplicativo
  - Use comentários para explicar "por quê" em vez de "o quê" ou "como"
  - Comentários são úteis para documentação de API pública

### Equilíbrio na Aplicação dos Princípios

- **Pragmatismo**: Os princípios são diretrizes, não regras rígidas. Use-os quando agregarem valor.
- **Evolução Gradual**: Aplique esses princípios incrementalmente; refatore código existente aos poucos.
- **Contexto é Importante**: Um microserviço simples pode não precisar da mesma estrutura que um sistema complexo.
- **Valor para o Projeto**: Priorize mudanças que trazem o maior benefício com o menor custo.
- **Comunicação em Equipe**: Discuta esses princípios com a equipe para alinhar expectativas e abordagens.

## Estrutura do Projeto

O projeto segue uma estrutura baseada em módulos do NestJS:

```
src/
├── core/              # Componentes centrais usados em toda a aplicação
├── infrastructure/    # Configurações e adaptadores de infraestrutura
├── modules/           # Módulos de funcionalidades
├── app.module.ts      # Módulo principal da aplicação
└── main.ts            # Ponto de entrada da aplicação
```

### Organização de Módulos

Cada módulo deve seguir a estrutura:

```
modules/feature/
├── controllers/       # Controladores da API
├── dto/               # Objetos de transferência de dados
├── entities/          # Entidades do banco de dados 
├── repositories/      # Repositórios para acesso a dados
├── services/          # Lógica de negócios
├── interfaces/        # Interfaces e tipos
├── feature.module.ts  # Definição do módulo
└── tests/             # Testes unitários e de integração
```

## Nomenclatura

### Diretórios e Arquivos

- Nomes de diretórios em kebab-case: `auth-service/`
- Nomes de arquivos em kebab-case: `user-profile.service.ts`
- Um arquivo por classe/componente
- Nome do arquivo deve refletir seu conteúdo: `user.controller.ts`

### Código

- Classes: PascalCase (ex: `UserService`)
- Interfaces: PascalCase com prefixo I (ex: `IUserRepository`)
- Tipos: PascalCase (ex: `UserPayload`)
- Variáveis e funções: camelCase (ex: `getUserById`)
- Constantes: UPPER_SNAKE_CASE (ex: `MAX_RETRY_COUNT`)
- Enums: PascalCase para nome, PascalCase para valores (ex: `enum UserRole { Admin, User }`)

## Comentários e Documentação

- Use JSDoc para documentar funções e classes públicas
- Adicione comentários para código complexo
- Mantenha os comentários atualizados
- Use o Swagger para documentar endpoints da API

Exemplo:

```typescript
/**
 * Recupera um usuário pelo ID
 * @param id - ID do usuário
 * @returns O usuário encontrado ou null se não existir
 */
async getUserById(id: string): Promise<User | null> {
  // Implementação
}
```

## Testes

- Escreva testes unitários para serviços e lógica de negócios
- Escreva testes de integração para APIs e fluxos completos
- Nomeie os testes de forma descritiva usando a metodologia "deve fazer X quando Y"
- Mantenha os testes independentes e isolados
- Use mocks e stubs apropriadamente

## Git e Commits

- Use o fluxo de trabalho Git Flow para gerenciar branches
- Nomeie branches de forma significativa: `feature/login-api`, `fix/auth-token-expiry`
- Escreva mensagens de commit claras e concisas
- Prefixe os commits com o tipo: `feat:`, `fix:`, `docs:`, `style:`, `refactor:`, `test:`, `chore:`
- Mantenha os commits pequenos e focados

### Hooks de Pré-commit

O projeto usa Husky e lint-staged para verificar o código antes do commit:

```json
"lint-staged": {
  "*.ts": [
    "eslint --fix",
    "prettier --write"
  ]
}
```

---

Este guia é um documento vivo e pode ser atualizado conforme necessário. Sinta-se à vontade para sugerir melhorias ou clarificações. 