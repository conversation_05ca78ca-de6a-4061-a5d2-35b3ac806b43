import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  SectorRepositoryPort,
  ListSectorsCriteria,
  ListSectorsResult,
} from '@/core/ports/repositories/sector-repository.port';
import { Sector } from '@/core/domain/entities/sector.entity';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaSectorRepository implements SectorRepositoryPort {
  constructor(private readonly prisma: PrismaService) {}

  async findByCode(code: string): Promise<Sector | null> {
    const sector = await this.prisma.sector.findUnique({
      where: { code },
    });
    if (!sector || sector.deletedAt) return null;
    return Sector.create(
      sector.id,
      sector.uuid,
      sector.code,
      sector.description,
      sector.createdBy,
      sector.updatedBy,
      sector.createdAt,
      sector.updatedAt,
    );
  }

  async findById(id: number): Promise<Sector | null> {
    const sector = await this.prisma.sector.findUnique({
      where: { id: id },
    });
    if (!sector || sector.deletedAt) return null;
    return Sector.create(
      sector.id,
      sector.uuid,
      sector.code,
      sector.description,
      sector.createdBy,
      sector.updatedBy,
      sector.createdAt,
      sector.updatedAt,
    );
  }

  async findByUuid(uuid: string): Promise<Sector | null> {
    const sector = await this.prisma.sector.findUnique({
      where: { uuid },
    });
    if (!sector || sector.deletedAt) return null;
    return Sector.create(
      sector.id,
      sector.uuid,
      sector.code,
      sector.description,
      sector.createdBy,
      sector.updatedBy,
      sector.createdAt,
      sector.updatedAt,
    );
  }

  async save(sector: Sector): Promise<Sector> {
    const data: Prisma.SectorCreateInput = {
      uuid: sector.uuid,
      code: sector.code,
      description: sector.description,
      createdBy: sector.createdBy,
      updatedBy: sector.updatedBy,
      createdAt: sector.createdAt,
      updatedAt: sector.updatedAt,
    };

    const savedSector = await this.prisma.sector.create({ data });

    if (!savedSector) {
      throw new Error('Failed to save sector');
    }

    return Sector.create(
      savedSector.id,
      savedSector.uuid,
      savedSector.code,
      savedSector.description,
      savedSector.createdBy,
      savedSector.updatedBy,
      savedSector.createdAt,
      savedSector.updatedAt,
    );
  }

  async update(sector: Sector): Promise<Sector> {
    const data: Prisma.SectorUpdateInput = {
      code: sector.code,
      description: sector.description,
      updatedBy: sector.updatedBy,
    };

    const updatedSector = await this.prisma.sector.update({
      where: { uuid: sector.uuid },
      data,
    });

    return Sector.create(
      updatedSector.id,
      updatedSector.uuid,
      updatedSector.code,
      updatedSector.description,
      updatedSector.createdBy,
      updatedSector.updatedBy,
      updatedSector.createdAt,
      updatedSector.updatedAt,
    );
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.sector.update({
      where: { uuid },
      data: { deletedAt: new Date() },
    });
  }

  async list(criteria: ListSectorsCriteria): Promise<ListSectorsResult> {
    const where: Prisma.SectorWhereInput = { deletedAt: null };
    if (criteria.code) {
      where.code = criteria.code;
    }
    if (criteria.description) {
      where.description = {
        contains: criteria.description,
        mode: 'insensitive',
      };
    }
    const [items, total] = await Promise.all([
      this.prisma.sector.findMany({
        where,
        skip: criteria.offset,
        take: criteria.limit,
        orderBy: {
          code: 'asc',
        },
      }),
      this.prisma.sector.count({ where }),
    ]);
    return {
      items: items.map((sector) =>
        Sector.create(
          sector.id,
          sector.uuid,
          sector.code,
          sector.description,
          sector.createdBy,
          sector.updatedBy,
          sector.createdAt,
          sector.updatedAt,
        ),
      ),
      total,
    };
  }
}
