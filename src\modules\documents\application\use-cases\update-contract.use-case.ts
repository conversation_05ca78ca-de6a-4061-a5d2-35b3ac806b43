import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IContractRepository } from '../../domain/repositories/contract.repository.interface';
import { Contract } from '../../domain/entities/contract.entity';

@Injectable()
export class UpdateContractUseCase {
  constructor(
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,
  ) { }

  async execute(contractUuid: string, patch: Partial<Contract>): Promise<Contract> {
    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract) {
      throw new NotFoundException('Contrato não encontrado');
    }
    return this.contractRepository.update(contractUuid, patch);
  }
} 