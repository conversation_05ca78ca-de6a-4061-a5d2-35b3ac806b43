/*
  Warnings:

  - You are about to drop the column `metodoPagamentoId` on the `customer_payment_preferences` table. All the data in the column will be lost.
  - Added the required column `paymentMethodId` to the `customer_payment_preferences` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "core"."customer_payment_preferences" DROP CONSTRAINT "customer_payment_preferences_metodoPagamentoId_fkey";

-- AlterTable
ALTER TABLE "core"."customer_payment_preferences" DROP COLUMN "metodoPagamentoId",
ADD COLUMN     "paymentMethodId" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "core"."customer_payment_preferences" ADD CONSTRAINT "customer_payment_preferences_paymentMethodId_fkey" FOREIGN KEY ("paymentMethodId") REFERENCES "core"."labels"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
