import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsInt, Min, IsString, MaxLength } from 'class-validator';

export class ListPaymentMethodDto {
  @ApiPropertyOptional({ description: 'Number of items per page', minimum: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  limit?: number;

  @ApiPropertyOptional({ description: 'Number of items to skip', minimum: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number;

  @ApiPropertyOptional({
    description: 'Filter by name',
    maxLength: 100,
    example: 'Credit Card',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  label?: string;

  constructor(limit: number, offset: number, label?: string) {
    this.limit = limit;
    this.offset = offset;
    this.label = label;
  }
}
