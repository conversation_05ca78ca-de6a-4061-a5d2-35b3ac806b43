import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class UpdatePaymentMethodDto {
  @ApiProperty({
    description: 'The label of the payment method',
    example: 'Credit Card',
  })
  @IsOptional()
  label?: string;
  @ApiProperty({
    description: 'The description of the payment method',
    example: 'A payment method using credit cards',
  })
  @IsOptional()
  description?: string;
}
