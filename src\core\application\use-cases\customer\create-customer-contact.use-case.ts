import { CustomerContact } from "@/core/domain/customer/entities/customer-contact.entity";
import { CustomerContactRepositoryPort } from "@/core/ports/repositories/customer-contact-repository.port";
import { Injectable, Inject } from '@nestjs/common';

@Injectable()
export class CreateCustomerContactUseCase {
  constructor(
    @Inject('CustomerContactRepository')
    private readonly customerContactRepository: CustomerContactRepositoryPort,
  ) {}

  async execute(customerId: number, contactData: Omit<CustomerContact, 'id' | 'customerId'>): Promise<CustomerContact> {
    const contact = await this.customerContactRepository.create({
      ...contactData,
      customerId,
    })

    return contact;
  }
}