import { Injectable } from '@nestjs/common';
import { PrismaService } from '@infrastructure/prisma/prisma.service';
import { PayablesTypeRepositoryPort } from '@core/ports/repositories/payables-type-repository.port';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';
import { Prisma, PayablesType as PrismaPayablesType } from '@prisma/client';

@Injectable()
export class PrismaPayablesTypeRepository
  implements PayablesTypeRepositoryPort
{
  constructor(private readonly prisma: PrismaService) {}

  private toDomain(prismaPayablesType: PrismaPayablesType): PayablesType {
    return new PayablesType(
      prismaPayablesType.uuid,
      prismaPayablesType.code,
      prismaPayablesType.description,
      prismaPayablesType.createdBy,
      prismaPayablesType.updatedBy,
      prismaPayablesType.createdAt,
      prismaPayablesType.updatedAt,
    );
  }

  async create(payablesType: PayablesType): Promise<PayablesType> {
    const created = await this.prisma.payablesType.create({
      data: {
        uuid: payablesType.id,
        code: payablesType.code,
        description: payablesType.description,
        createdBy: payablesType.createdBy,
        updatedBy: payablesType.updatedBy,
      },
    });
    return this.toDomain(created);
  }

  async findByCode(code: string): Promise<PayablesType | null> {
    const payablesType = await this.prisma.payablesType.findUnique({
      where: { code },
    });
    if (!payablesType || payablesType.deletedAt) return null;
    return this.toDomain(payablesType);
  }

  async findById(id: number): Promise<PayablesType | null> {
    const payablesType = await this.prisma.payablesType.findUnique({
      where: { id },
    });
    if (!payablesType || payablesType.deletedAt) return null;
    return this.toDomain(payablesType);
  }

  async findByUuid(uuid: string): Promise<PayablesType | null> {
    const payablesType = await this.prisma.payablesType.findUnique({
      where: { uuid },
    });
    if (!payablesType || payablesType.deletedAt) return null;
    return this.toDomain(payablesType);
  }

  async findAll(): Promise<PayablesType[]> {
    const payablesTypes = await this.prisma.payablesType.findMany({
      where: { deletedAt: null },
    });
    return payablesTypes.map((pt) => this.toDomain(pt));
  }

  async findWithPagination(params: {
    limit: number;
    offset: number;
    code?: string;
    description?: string;
  }): Promise<{ items: PayablesType[]; total: number }> {
    const where: Prisma.PayablesTypeWhereInput = { deletedAt: null };
    if (params.code) {
      where.code = {
        contains: params.code,
        mode: 'insensitive',
      };
    }
    if (params.description) {
      where.description = {
        contains: params.description,
        mode: 'insensitive',
      };
    }
    const [items, total] = await Promise.all([
      this.prisma.payablesType.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.payablesType.count({ where }),
    ]);
    return {
      items: items.map((payablesType) => this.toDomain(payablesType)),
      total,
    };
  }

  async update(payablesType: PayablesType): Promise<PayablesType> {
    const updated = await this.prisma.payablesType.update({
      where: { uuid: payablesType.id },
      data: {
        code: payablesType.code,
        description: payablesType.description,
        updatedBy: payablesType.updatedBy,
      },
    });
    return this.toDomain(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.payablesType.update({
      where: { uuid: id },
      data: { deletedAt: new Date() },
    });
  }
}
