import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ContactsService } from './contacts.service';
import { ContactResponseDto, CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

@ApiTags('Customer Contacts')
@Controller('core/customers/:uuid/contacts')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ContactsController {

  constructor(private readonly contactsService: ContactsService) { }

  @Post()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'Create customer contacts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Customer UUID',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          contact: { type: 'string' },
          type: { type: 'string' },
          area: { type: 'string' },
          responsible: { type: 'string' },
        },
        required: ['contact', 'type', 'area', 'responsible'],
        example: {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Finance',
          responsible: 'Maria Silva',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Contatos criados com sucesso.',
    type: [ContactResponseDto],
  })
  create(
    @Param('uuid') uuid: string,
    @Body() createContactDto: Array<CreateContactDto>): Promise<ContactResponseDto[]> {
    return this.contactsService.create(uuid, createContactDto);
  }

  @Get()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'List customer contacts' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Customer UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de contatos do customer retornada com sucesso.',
    type: [ContactResponseDto],
  })
  async findAll(@Param('uuid') uuid: string,) {
    return this.contactsService.findAll(uuid);
  }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.contactsService.findOne(+id);
  // }

  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Customer UUID',
    type: String,
  })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateContactDto: UpdateContactDto) {
    return this.contactsService.update(id, updateContactDto);
  }

  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Customer UUID',
    type: String,
  })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.contactsService.remove(id);
  }
}
