generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "darwin-arm64", "debian-openssl-3.0.x", "darwin"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["core", "events", "finance"]
}

model User {
  id             String    @id @default(uuid())
  email          String    @unique @db.VarChar(255)
  name           String    @db.VarChar(255)
  password       String    @db.VarChar(255)
  role           Role      @default(USER)
  keycloakId     String?   @map("keycloak_id") @db.Var<PERSON>har(50)
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")
  createdDomains Domain[]  @relation("DomainCreator")
  updatedDomains Domain[]  @relation("DomainUpdater")
  supplier       Supplier?
  otps           UserOtp[]
  uploadedCertificates Certificate[]
  employee       Employee?

  @@map("users")
  @@schema("core")
}

model UserOtp {
  id         String   @id @default(uuid())
  user       User     @relation(fields: [userId], references: [id])
  userId     String
  hash       String
  expiresAt  DateTime
  createdAt  DateTime @default(now())

  @@index([userId, expiresAt])
  @@map("users_otp")
  @@schema("core")
}

model Supplier {
  id             String                 @id @default(uuid())
  name           String                 @db.VarChar(100)
  document       String                 @unique @db.Char(14)
  tradeName      String?                @map("trade_name") @db.VarChar(100)
  address        Json
  classification SupplierClassification @default(CORE)
  type           SupplierType           @default(BANK)
  status         SupplierStatus         @default(PENDING)
  userId         String?                @unique @map("user_id") @db.VarChar(50)
  createdAt      DateTime               @default(now()) @map("created_at")
  createdBy      String                 @map("created_by") @db.VarChar(50)
  updatedAt      DateTime               @updatedAt @map("updated_at")
  updatedBy      String                 @map("updated_by") @db.VarChar(50)
  deletedAt      DateTime?              @map("deleted_at")
  email          String                 @unique @db.VarChar(255)
  contacts       SupplierContact[]
  user           User?                  @relation(fields: [userId], references: [id])
  stateRegistration String?             @db.VarChar(30) 
  municipalRegistration String?         @db.VarChar(30) 
  taxRegime      TaxRegime? 
  companySize    CompanySize? 

  @@index([name])
  @@index([document])
  @@index([userId])
  @@map("suppliers")
  @@schema("core")
}

model Company {
  id          Int           @id @default(autoincrement())
  uuid        String        @unique
  cnpj        String        @unique
  address     Json
  phone       String
  email       String
  status      CompanyStatus @default(ACTIVE)
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")
  createdBy   String        @map("created_by")
  updatedBy   String        @map("updated_by")
  deletedAt   DateTime?     @map("deleted_at")
  razaoSocial String        @unique

  @@map("companies")
  @@schema("core")
}

model PaymentMethod {
  id          Int       @id @default(autoincrement())
  uuid        String    @unique @default(uuid())
  label       String    @unique @db.VarChar(100)
  description String?   @db.VarChar(255)
  createdBy   String    @map("created_by")
  updatedBy   String    @map("updated_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@map("payment_methods")
  @@schema("finance")
}

model Sector {
  id          Int       @id @default(autoincrement())
  uuid        String    @unique @default(uuid())
  code        String    @unique @db.VarChar(50)
  description String    @db.VarChar(255)
  createdBy   String    @map("created_by")
  updatedBy   String    @map("updated_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@index([code])
  @@index([description])
  @@map("sectors")
  @@schema("finance")
}

model DomainEvent {
  id            String   @id @default(uuid())
  eventId       String   @unique
  eventName     String
  occurredAt    DateTime
  correlationId String?
  data          Json
  createdAt     DateTime @default(now()) @map("created_at")

  @@map("domain_events")
  @@schema("events")
}

model PayablesType {
  id          Int       @id @default(autoincrement())
  uuid        String    @unique @default(uuid())
  code        String    @unique @db.VarChar(50)
  description String    @db.VarChar(255)
  createdBy   String    @map("created_by")
  updatedBy   String    @map("updated_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@index([code])
  @@index([description])
  @@map("payables_types")
  @@schema("finance")
}

model Employee {
  id                 Int            @id @default(autoincrement())
  uuid               String         @unique @default(uuid())
  name               String         @db.VarChar(255)
  email              String         @unique @db.VarChar(320)
  position           String         @db.VarChar(100)
  department         String         @db.VarChar(100)
  hireDate           DateTime       @map("hire_date")
  address            Json?
  personalDocuments  Json?          @map("personal_documents")
  dependents         Json?
  status             EmployeeStatus @default(ACTIVE)
  workSchedule       Json?          @map("work_schedule")
  shift              String?        @map("shift")
  grossSalary        Decimal?       @map("gross_salary") @db.Decimal(10, 2)
  mealAllowance      Decimal?       @map("meal_allowance") @db.Decimal(10, 2)
  transportAllowance Decimal?       @map("transport_allowance") @db.Decimal(10, 2)
  healthPlan         String?        @map("health_plan") @db.VarChar(100)
  contractType       String?        @map("contract_type")
  seniority          String?        @map("seniority")
  phone              String?        @db.VarChar(20)
  birthDate          DateTime?      @map("birth_date")
  workHours          String?        @map("work_hours") @db.VarChar(50)
  overtimeBank       Boolean?       @default(false) @map("overtime_bank")
  vacations          Json?          @map("vacations")
  createdBy          String         @map("created_by")
  updatedBy          String         @map("updated_by")
  createdAt          DateTime       @default(now()) @map("created_at")
  updatedAt          DateTime       @updatedAt @map("updated_at")
  deletedAt          DateTime?      @map("deleted_at")
  archives           Archive[]
  userId             String?        @unique @map("user_id") @db.VarChar(50)
  user               User?          @relation(fields: [userId], references: [id])

  @@index([name])
  @@index([email])
  @@index([position])
  @@index([department])
  @@map("employees")
  @@schema("core")
}

model Archive {
  id             String    @id @default(uuid())
  employeeUuid   String    @map("employee_uuid")
  employee       Employee  @relation(fields: [employeeUuid], references: [uuid])
  uploadedBy     String    @map("uploaded_by")
  filePath       String    @map("file_path")
  fileName       String?   @map("file_name") @db.VarChar(255)
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  @@index([employeeUuid])
  @@index([uploadedBy])
  @@index([createdAt])
  @@index([employeeUuid, deletedAt])
  @@map("archives")
  @@schema("core")
}

// Modelo de Cliente
model Customer {
  id                 Int                         @id @default(autoincrement())
  uuid               String                      @unique @default(uuid())
  razaoSocial        String                      @db.VarChar(255)
  cnpj               String                      @unique @db.VarChar(20)
  email              String                      @unique @db.VarChar(320)
  phone              String?                     @db.VarChar(20)
  address            Json?
  image              String?
  status             CustomerStatus              @default(ACTIVE)
  userId             String?                     @unique @map("user_id") @db.VarChar(50)
  url                String                      @default("") @db.VarChar(100)
  createdAt          DateTime                    @default(now()) @map("created_at")
  createdBy          String                      @map("created_by")
  updatedAt          DateTime                    @updatedAt @map("updated_at")
  updatedBy          String                      @map("updated_by")
  deletedAt          DateTime?                   @map("deleted_at")
  documents          CustomerDocument[]
  paymentPreferences CustomerPaymentPreference[]
  domains            Domain[]
  // Auto-relacionamento
  parentId           Int?                        @map("parent_id")
  parent             Customer?                   @relation("CustomerToParent", fields: [parentId], references: [id])
  children           Customer[]                  @relation("CustomerToParent")
  contacts      CustomerContact[]
  certificates  Certificate[]
  
  @@index([razaoSocial])
  @@index([cnpj])
  @@index([email])
  @@map("customers")
  @@schema("core")
}

model CustomerContact {
  id          String   @id @default(uuid())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id])
  contact     String
  type        String
  area        String
  responsible String
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")
  @@index([customerId])
  @@map("customer_contacts")
  @@schema("core")
}

model CostCenter {
  id          Int       @id @default(autoincrement())
  uuid        String    @unique @default(uuid())
  description String    @db.VarChar(255)
  createdBy   String    @map("created_by")
  updatedBy   String    @map("updated_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@index([description])
  @@map("finance.cost_centers")
  @@schema("finance")
}

model PasswordResetToken {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([token])
  @@index([userId])
  @@index([expiresAt])
  @@map("password_reset_tokens")
  @@schema("core")
}

model Contract {
  id             String            @id @default(uuid())
  entityType     EntityType
  entityUuid     String
  contractType   ContractType
  currentVersion Int               @default(1)
  status         ContractStatus    @default(PENDING)
  createdAt      DateTime          @default(now()) @map("created_at")
  updatedAt      DateTime          @updatedAt @map("updated_at")
  createdBy      String            @map("created_by") @db.VarChar(50)
  updatedBy      String            @map("updated_by") @db.VarChar(50)
  versions       ContractVersion[]

  @@index([entityType, entityUuid])
  @@map("contracts")
  @@schema("core")
}

model ContractVersion {
  id             String    @id @default(uuid())
  versionId      Int
  uploadedAt     DateTime  @default(now())
  uploadedBy     String    @db.VarChar(50)
  filePath       String
  signed         Boolean   @default(false)
  validatedBy    String?   @db.VarChar(50)
  validatedAt    DateTime?
  expirationDate DateTime?
  contractId     String
  createdAt      DateTime  @default(now()) @map("created_at")
  contract       Contract  @relation(fields: [contractId], references: [id])

  @@unique([contractId, versionId])
  @@map("contract_versions")
  @@schema("core")
}

model Document {
  id             String            @id @default(uuid())
  entityType     EntityType
  entityUuid     String
  uploadedBy     String
  currentVersion Int
  status         DocumentStatus    @default(ACTIVE)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  updatedBy      String?           @map("updated_by")
  responsible    String?
  department     String?
  description    String?
  versions       DocumentVersion[]

  @@map("document")
  @@schema("core")
}

model DocumentVersion {
  id             String    @id @default(uuid())
  versionId      Int
  uploadedAt     DateTime  @default(now())
  uploadedBy     String
  expirationDate DateTime?
  filePath       String
  documentId     String
  createdAt      DateTime  @default(now())
  document       Document  @relation(fields: [documentId], references: [id])

  @@unique([documentId, versionId])
  @@map("document_version")
  @@schema("core")
}

model CustomerDocument {
  id             String    @id @default(uuid())
  customerUuid   String
  name           String    @db.VarChar(255)
  url            String
  fileName       String?   @map("file_name") @db.VarChar(255)
  responsible    String?   @db.VarChar(255)
  department     String?   @db.VarChar(255)
  description    String?   @db.Text
  expirationDate String?   @map("expiration_date") @db.VarChar(10)
  uploadedBy     String?   @map("uploaded_by")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")
  customer       Customer  @relation(fields: [customerUuid], references: [uuid], onDelete: Cascade)

  @@index([customerUuid])
  @@map("customer_documents")
  @@schema("core")
}

model Label {
  id          Int      @id @default(autoincrement())
  label       String   @db.VarChar(100)
  idComponent String   @db.VarChar(100)
  modulo      String   @db.VarChar(100)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("labels")
  @@schema("core")
}

model CustomerPaymentPreference {
  id                String   @id @default(uuid())
  customerId        String   @map("customer_id")
  department        String   @db.VarChar(100)
  responsible       String   @db.VarChar(100)
  paymentMethodId   String   @map("payment_method_id")
  paymentCondition  String   @map("payment_condition") @db.VarChar(100)
  billingPreference String   @map("billing_preference") @db.VarChar(100)
  costCenterId      String   @map("cost_center_id")
  sectorId          String   @map("sector_id")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  customer          Customer @relation(fields: [customerId], references: [uuid])

  @@index([customerId])
  @@index([paymentMethodId])
  @@index([costCenterId])
  @@index([sectorId])
  @@map("customer_payment_preferences")
  @@schema("core")
}

model SupplierContact {
  id          String    @id @default(uuid())
  supplierId  String
  contact     String
  type        String
  area        String
  responsible String
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")
  supplier    Supplier  @relation(fields: [supplierId], references: [id])

  @@index([supplierId])
  @@map("supplier_contacts")
  @@schema("core")
}

model Service {
  id          String     @id @default(uuid())
  entityType  EntityType
  entityUuid  String
  type        String     @db.VarChar(100)
  rate        String     @db.VarChar(50)
  description String
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  createdBy   String     @map("created_by")
  updatedBy   String     @map("updated_by")
  deletedAt   DateTime?  @map("deleted_at")

  @@index([entityType, entityUuid])
  @@index([type])
  @@map("services")
  @@schema("core")
}

model Domain {
  id           Int        @id @default(autoincrement())
  uuid         String     @unique @default(uuid())
  customerUuid String     @map("customer_uuid")
  domain       String
  licenseNumber String 
  licenseType  LicenseType
  brandName    String
  notes        String?
  createdAt    DateTime   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime   @updatedAt @map("updated_at") @db.Timestamptz(6)
  deletedAt    DateTime?  @map("deleted_at") @db.Timestamptz(6)
  createdBy    String     @map("created_by")
  updatedBy    String     @map("updated_by")
  creator      User       @relation("DomainCreator", fields: [createdBy], references: [id])
  customer     Customer   @relation(fields: [customerUuid], references: [uuid])
  updater      User       @relation("DomainUpdater", fields: [updatedBy], references: [id])

  @@index([customerUuid])
  @@index([domain])
  @@index([customerUuid, domain, deletedAt])
  @@map("domains")
  @@schema("core")
}

model Certificate {
  id           String              @id @default(uuid())
  customerId   Int
  customer     Customer            @relation(fields: [customerId], references: [id])
  category     CertificateCategory
  type         CertificateType
  fileUrl      String
  notes        String?
  uploadedById String
  uploadedBy   User                @relation(fields: [uploadedById], references: [id])
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt

  @@map("certificates")
  @@schema("core")
}

enum Role {
  ADMIN
  USER
  FINANCE_ADMIN
  FINANCE_USER
  DOCUMENT_ARCHIVER
  DOCUMENT_UPLOADER
  DOCUMENT_VIEWER
  DOCUMENT_DOWNLOADER
  SUPPLIER_VIEWER
  CUSTOMER_VIEWER
  EMPLOYEE

  @@schema("core")
}

enum SupplierStatus {
  ACTIVE
  INACTIVE
  PENDING

  @@schema("core")
}

enum SupplierClassification {
  CORE
  GENERAL

  @@schema("core")
}

enum SupplierType {
  BANK
  GAME
  SPORTSBOOK
  KYC
  OTHER

  @@schema("core")
}

enum CompanyStatus {
  ACTIVE
  INACTIVE

  @@schema("core")
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE

  @@schema("core")
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  PENDING

  @@schema("core")
}

enum ContractType {
  CERT_GAME
  CERT_RNG
  CERT_RGS
  CERT_PLATFORM
  CERT_INTEGRATION
  CERT_KYC
  CERT_PAYMENT
  CERT_SPORTSBOOK

  @@schema("core")
}

enum ContractStatus {
  PENDING
  APPROVED
  REJECTED

  @@schema("core")
}

enum EntityType {
  CLIENT
  COLLABORATE
  SUPPLIER

  @@schema("core")
}

enum DocumentStatus {
  ACTIVE
  ARCHIVED

  @@schema("core")
}

enum LicenseType {
  FEDERAL
  ESTADUAL
  @@schema("core")
}

enum TaxRegime {
  SIMPLES_NACIONAL
  LUCRO_PRESUMIDO
  LUCRO_REAL
  MEI

  @@schema("core")
}

enum CompanySize {
  MEI
  MICROEMPRESA
  PEQUENO_PORTE
  MEDIO_PORTE
  GRANDE_PORTE

  @@schema("core")
}

enum CertificateCategory {
  FORNECEDORES_JOGOS
  FORNECEDORES_KYC
  PAGAMENTOS
  SPORTSBOOK

  @@schema("core")
}

enum CertificateType {
  CERTIFICADO_DE_JOGO
  CERTIFICADO_RNG
  CERTIFICADO_RGS
  CERTIFICADO_DE_PLATAFORMA
  CERTIFICADO_DE_INTEGRACAO
  CERTIFICADO_DE_KYC
  CERTIFICADO_DE_MEIOS_DE_PAGAMENTO
  CERTIFICADO_DE_SPORTSBOOK

  @@schema("core")
}
