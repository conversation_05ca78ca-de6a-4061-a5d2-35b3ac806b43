import { InternalServerErrorException } from '@nestjs/common';
import axios, { AxiosResponse, AxiosError } from 'axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RequestUtilsService {
  private readonly maxRetries: number = 3;
  private readonly initialDelayMs: number = 1000;
  constructor(private readonly configService: ConfigService) {
    this.maxRetries = this.configService.get<number>('MAX_RETRIES', 3);
    this.initialDelayMs = this.configService.get<number>(
      'INITIAL_RETRY_DELAY_MS',
      1000,
    );
  }

  async executeWithRetry<T>(
    request: () => Promise<AxiosResponse<T>>,
    exceptionfn: (error: unknown) => void,
  ): Promise<T> {
    let attempt = 0;

    while (attempt < this.maxRetries) {
      try {
        const response = await request();
        return response.data;
      } catch (error) {
        attempt++;
        const isLastAttempt = attempt >= this.maxRetries;

        if (this.isRetryableError(error)) {
          if (isLastAttempt) {
            throw new InternalServerErrorException(
              'Falha após tentativas máximas',
            );
          }
          const delay = this.initialDelayMs * Math.pow(2, attempt - 1);
          await new Promise((resolve) => setTimeout(resolve, delay));
        } else {
          exceptionfn(error);
        }
      }
    }

    throw new InternalServerErrorException('Falha após tentativas máximas');
  }

  isRetryableError(error: unknown): boolean {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      // Falha de rede (sem resposta) ou erro 5xx
      return (
        !axiosError.response?.data ||
        (axiosError.response.status >= 500 && axiosError.response.status <= 599)
      );
    }
    return false;
  }
}
