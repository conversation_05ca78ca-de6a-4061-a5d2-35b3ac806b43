import { AggregateRoot } from '../shared/aggregate-root';

export class PaymentMethod extends AggregateRoot {
  readonly id?: number;
  readonly uuid: string;
  private _label: string;
  private _description: string;
  readonly createdAt: Date;
  private _updatedAt: Date;
  readonly createdBy: string;
  private _updatedBy: string;

  constructor(
    id: number | undefined,
    uuid: string,
    label: string,
    description: string,
    createdBy: string,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    updatedBy: string = createdBy,
  ) {
    super();
    this.id = id;
    this.uuid = uuid;
    this._label = label;
    this._description = description;
    this.createdAt = createdAt;
    this._updatedAt = updatedAt;
    this.createdBy = createdBy;
    this._updatedBy = updatedBy;
  }
  get label(): string {
    return this._label;
  }
  get description(): string {
    return this._description;
  }
  get updatedAt(): Date {
    return this._updatedAt;
  }
  get updatedBy(): string {
    return this._updatedBy;
  }

  updateLabel(label: string): void {
    this._label = label;
  }
  updateDescription(description: string): void {
    this._description = description;
  }
  s;
  updateUpdatedAt(updatedAt: Date): void {
    this._updatedAt = updatedAt;
  }
  updateUpdatedBy(updatedBy: string): void {
    this._updatedBy = updatedBy;
  }
  updatePaymentMethod(
    label: string,
    description: string,
    updatedBy: string,
  ): void {
    this._label = label;
    this._description = description;
    this._updatedBy = updatedBy;
  }

  toJSON(): object {
    return {
      id: this.id,
      uuid: this.uuid,
      label: this._label,
      description: this._description,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this._updatedAt,
      updatedBy: this._updatedBy,
    };
  }
}
