import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, IsUUID, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LicenseType } from '@prisma/client';

export class CreateDomainDto {
  @ApiProperty({
    description: 'Domain name',
    example: 'example.com',
  })
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, {
    message: 'Invalid domain format',
  })
  domain: string;

  @ApiProperty({
    description: 'Domain type',
    enum: LicenseType,
    example: LicenseType.ESTADUAL,
  })
  @IsNotEmpty()
  @IsEnum(LicenseType)
  licenseType: LicenseType;

  @ApiProperty({
    description: 'License number',
    example: '**********',
  })
  @IsNotEmpty()
  licenseNumber: string;

  @ApiProperty({
    description: 'Brand name',
    example: 'Brand Name',
  })
  @IsNotEmpty()
  brandName: string;

  @ApiProperty({
    description: 'Notes',
    example: 'Notes',
  })
  @IsOptional()
  notes: string | null;
} 