import { ApiProperty } from '@nestjs/swagger';
import { IsString, MaxLength, MinLength } from 'class-validator';

export class CreatePaymentMethodDto {
  @ApiProperty({
    example: 'Credit Card',
    description: 'Nome do método de pagamento',
  })
  @IsString({
    message: 'O nome deve ser uma string válida',
  })
  @MaxLength(100, {
    message: 'O nome deve ter no máximo 100 caracteres',
  })
  @MinLength(3, {
    message: 'O nome deve ter no mínimo 3 caracteres',
  })
  label: string;

  @ApiProperty({
    example: 'Método de pagamento via cartão de crédito',
    description: 'Descrição do método de pagamento',
  })
  @IsString({
    message: 'A descrição deve ser uma string válida',
  })
  @MaxLength(500, {
    message: 'A descrição deve ter no máximo 500 caracteres',
  })
  description: string;
}
