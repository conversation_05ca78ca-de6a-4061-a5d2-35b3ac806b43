import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EntityType } from '../../../core/domain/service/enums/entity-type.enum';
import { Service } from '../../../core/domain/service/entities/service.entity';

export class ServiceResponseDto {
  @ApiProperty({
    description: 'ID único do serviço',
    example: 'e3f86be7-19d6-4f4c-8024-f6aa4dbaec8e',
  })
  id: string;

  @ApiProperty({
    description: 'Tipo da entidade',
    enum: EntityType,
    example: EntityType.SUPPLIER,
  })
  entityType: EntityType;

  @ApiProperty({
    description: 'UUID da entidade vinculada',
    example: '40c4b8cb-3ce7-4cb2-aada-04ec9aa987e4',
  })
  entityUuid: string;

  @ApiProperty({
    description: 'Tipo do serviço',
    example: 'Consultoria Financeira',
  })
  type: string;

  @ApiProperty({
    description: 'Taxa do serviço',
    example: '5.5%',
  })
  rate: string;

  @ApiProperty({
    description: 'Descrição do serviço',
    example: 'Serviço de consultoria financeira especializada',
  })
  description: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2024-11-10T10:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Usuário que criou',
    example: 'user-uuid',
  })
  createdBy: string;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2024-11-10T11:00:00Z',
  })
  updatedAt: string;

  @ApiProperty({
    description: 'Usuário que atualizou',
    example: 'user-uuid',
  })
  updatedBy: string;

  @ApiPropertyOptional({
    description: 'Data de exclusão (soft delete)',
    example: '2024-11-10T12:00:00Z',
  })
  deletedAt?: string;

  static fromEntity(service: Service): ServiceResponseDto {
    return {
      id: service.id,
      entityType: service.entityType,
      entityUuid: service.entityUuid,
      type: service.type,
      rate: service.rate,
      description: service.description,
      createdAt: service.createdAt.toISOString(),
      createdBy: service.createdBy,
      updatedAt: service.updatedAt.toISOString(),
      updatedBy: service.updatedBy,
      deletedAt: service.deletedAt?.toISOString(),
    };
  }
}

export class PaginatedServicesResponseDto {
  @ApiProperty({
    description: 'Lista de serviços',
    type: [ServiceResponseDto],
  })
  items: ServiceResponseDto[];

  @ApiProperty({
    description: 'Total de itens',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'Limite por página',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Offset da página',
    example: 0,
  })
  offset: number;
} 