import { Inject, Injectable } from "@nestjs/common";
import { IContractRepository } from "../../domain/repositories/contract.repository.interface";
import { Contract } from "../../domain/entities/contract.entity";

@Injectable()
export class ListContractUseCase {
  constructor(
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,
  ) {}

  async execute(filters: Partial<Pick<Contract, 'status' | 'entityType' | 'entityUuid'>>, limit: number, offset: number): Promise<{ items: Contract[]; total: number }> {
    return this.contractRepository.list(filters, limit, offset);
  }
}