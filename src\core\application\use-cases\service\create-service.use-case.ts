import { Injectable, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { ServiceRepositoryPort } from '../../../ports/repositories/service-repository.port';
import { Service } from '../../../domain/service/entities/service.entity';
import {
  CreateServiceDto,
  ServiceItemDto,
} from '../../../../modules/finance/dto/create-service.dto';
import { EntityType } from '../../../domain/service/enums/entity-type.enum';

@Injectable()
export class CreateServiceUseCase {
  constructor(
    @Inject('SERVICE_REPOSITORY')
    private readonly serviceRepository: ServiceRepositoryPort,
  ) {}

  async execute(dto: CreateServiceDto, createdBy: string): Promise<Service> {
    const service = new Service(
      uuidv4(),
      dto.entityType,
      dto.entityUuid,
      dto.type,
      dto.rate,
      dto.description,
      createdBy,
    );

    return await this.serviceRepository.create(service);
  }

  async executeMultiple(
    services: ServiceItemDto[],
    entityType: EntityType,
    entityUuid: string,
    createdBy: string,
  ): Promise<Service[]> {
    const serviceEntities = services.map(
      (serviceData) =>
        new Service(
          uuidv4(),
          entityType,
          entityUuid,
          serviceData.type,
          serviceData.rate,
          serviceData.description,
          createdBy,
        ),
    );

    const createdServices = await Promise.all(
      serviceEntities.map((service) => this.serviceRepository.create(service)),
    );

    return createdServices;
  }
}
