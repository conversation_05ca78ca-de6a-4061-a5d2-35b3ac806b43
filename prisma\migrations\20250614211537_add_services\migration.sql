-- CreateTable
CREATE TABLE "core"."services" (
    "id" TEXT NOT NULL,
    "entityType" "core"."EntityType" NOT NULL,
    "entityUuid" TEXT NOT NULL,
    "type" VARCHAR(100) NOT NULL,
    "rate" VARCHAR(50) NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "services_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "services_entityType_entityUuid_idx" ON "core"."services"("entityType", "entityUuid");

-- CreateIndex
CREATE INDEX "services_type_idx" ON "core"."services"("type");
