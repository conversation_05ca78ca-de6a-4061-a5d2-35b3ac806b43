import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FileReaderService } from '../services/file-reader.service';
import { FileReaderOptions } from '../interfaces/file-reader.interface';

interface BatchProcessingOptions {
  batchSize?: number;
  requiredColumns?: string[];
  validateData?: boolean;
}

interface BatchProcessingResult {
  totalProcessed: number;
  totalBatches: number;
  successfulRecords: number;
  failedRecords: number;
  errors: Array<{
    batch: number;
    record: number;
    message: string;
    data?: any;
  }>;
  processingTime: number;
}

@ApiTags('Batch Processing Examples')
@Controller('examples/batch-processing')
export class BatchProcessingController {
  constructor(private readonly fileReaderService: FileReaderService) {}

  @Post('large-file')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Process large file in batches' })
  @ApiResponse({ status: 200, description: 'File processed successfully in batches' })
  @ApiResponse({ status: 400, description: 'Invalid file or processing error' })
  async processLargeFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: BatchProcessingOptions = {},
  ): Promise<BatchProcessingResult> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const startTime = Date.now();
    const batchSize = options.batchSize || 1000;
    
    const result: BatchProcessingResult = {
      totalProcessed: 0,
      totalBatches: 0,
      successfulRecords: 0,
      failedRecords: 0,
      errors: [],
      processingTime: 0,
    };

    const fileReaderOptions: FileReaderOptions = {
      requiredColumns: options.requiredColumns,
      trimValues: true,
      skipEmptyLines: true,
    };

    try {
      const batchResult = await this.fileReaderService.processFileInBatches(
        file,
        fileReaderOptions,
        batchSize,
        async (batch: Record<string, any>[], batchNumber: number) => {
          console.log(`Processing batch ${batchNumber} with ${batch.length} records`);
          
          // Process each record in the batch
          for (let i = 0; i < batch.length; i++) {
            const record = batch[i];
            
            try {
              // Validate data if required
              if (options.validateData) {
                this.validateRecord(record);
              }

              // Simulate processing (e.g., save to database)
              await this.processRecord(record);
              
              result.successfulRecords++;
            } catch (error) {
              result.failedRecords++;
              result.errors.push({
                batch: batchNumber,
                record: i + 1,
                message: error.message,
                data: record,
              });
            }
          }
        }
      );

      result.totalProcessed = batchResult.totalProcessed;
      result.totalBatches = batchResult.batches;
      result.processingTime = Date.now() - startTime;

      return result;
    } catch (error) {
      throw new BadRequestException(`Batch processing failed: ${error.message}`);
    }
  }

  @Post('customers-bulk-import')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Bulk import customers with batch processing' })
  @ApiResponse({ status: 200, description: 'Customers imported successfully' })
  async bulkImportCustomers(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: { batchSize?: number } = {},
  ): Promise<{
    summary: BatchProcessingResult;
    importedCustomers: number;
    duplicatesSkipped: number;
  }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const startTime = Date.now();
    const batchSize = options.batchSize || 500;
    const processedEmails = new Set<string>();
    
    let importedCustomers = 0;
    let duplicatesSkipped = 0;

    const result: BatchProcessingResult = {
      totalProcessed: 0,
      totalBatches: 0,
      successfulRecords: 0,
      failedRecords: 0,
      errors: [],
      processingTime: 0,
    };

    const fileReaderOptions: FileReaderOptions = {
      requiredColumns: ['name', 'email'],
      trimValues: true,
      skipEmptyLines: true,
    };

    try {
      const batchResult = await this.fileReaderService.processFileInBatches(
        file,
        fileReaderOptions,
        batchSize,
        async (batch: Record<string, any>[], batchNumber: number) => {
          console.log(`Processing customer batch ${batchNumber} with ${batch.length} records`);
          
          const customersToImport: any[] = [];
          
          // Validate and prepare batch
          for (let i = 0; i < batch.length; i++) {
            const record = batch[i];
            
            try {
              const customer = this.normalizeCustomerData(record);
              
              // Check for duplicates
              if (processedEmails.has(customer.email.toLowerCase())) {
                duplicatesSkipped++;
                continue;
              }
              
              processedEmails.add(customer.email.toLowerCase());
              customersToImport.push(customer);
              result.successfulRecords++;
              
            } catch (error) {
              result.failedRecords++;
              result.errors.push({
                batch: batchNumber,
                record: i + 1,
                message: error.message,
                data: record,
              });
            }
          }

          // Bulk insert customers (simulated)
          if (customersToImport.length > 0) {
            await this.bulkInsertCustomers(customersToImport);
            importedCustomers += customersToImport.length;
          }
        }
      );

      result.totalProcessed = batchResult.totalProcessed;
      result.totalBatches = batchResult.batches;
      result.processingTime = Date.now() - startTime;

      return {
        summary: result,
        importedCustomers,
        duplicatesSkipped,
      };
    } catch (error) {
      throw new BadRequestException(`Customer bulk import failed: ${error.message}`);
    }
  }

  @Post('data-transformation')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Transform data while processing in batches' })
  @ApiResponse({ status: 200, description: 'Data transformed successfully' })
  async transformData(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: { 
      batchSize?: number;
      transformations?: string[];
    } = {},
  ): Promise<{
    summary: BatchProcessingResult;
    transformedData: any[];
  }> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const startTime = Date.now();
    const batchSize = options.batchSize || 1000;
    const transformedData: any[] = [];
    
    const result: BatchProcessingResult = {
      totalProcessed: 0,
      totalBatches: 0,
      successfulRecords: 0,
      failedRecords: 0,
      errors: [],
      processingTime: 0,
    };

    try {
      const batchResult = await this.fileReaderService.processFileInBatches(
        file,
        { trimValues: true, skipEmptyLines: true },
        batchSize,
        async (batch: Record<string, any>[], batchNumber: number) => {
          console.log(`Transforming batch ${batchNumber} with ${batch.length} records`);
          
          for (let i = 0; i < batch.length; i++) {
            const record = batch[i];
            
            try {
              const transformed = this.applyTransformations(record, options.transformations || []);
              transformedData.push(transformed);
              result.successfulRecords++;
            } catch (error) {
              result.failedRecords++;
              result.errors.push({
                batch: batchNumber,
                record: i + 1,
                message: error.message,
                data: record,
              });
            }
          }
        }
      );

      result.totalProcessed = batchResult.totalProcessed;
      result.totalBatches = batchResult.batches;
      result.processingTime = Date.now() - startTime;

      return {
        summary: result,
        transformedData: transformedData.slice(0, 100), // Return first 100 for preview
      };
    } catch (error) {
      throw new BadRequestException(`Data transformation failed: ${error.message}`);
    }
  }

  private validateRecord(record: Record<string, any>): void {
    // Basic validation example
    if (!record.name || record.name.trim().length === 0) {
      throw new Error('Name is required');
    }
    
    if (record.email && !this.isValidEmail(record.email)) {
      throw new Error(`Invalid email format: ${record.email}`);
    }
  }

  private async processRecord(record: Record<string, any>): Promise<void> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1));
    
    // Here you would typically save to database or perform other operations
    console.log(`Processing record: ${record.name}`);
  }

  private normalizeCustomerData(record: Record<string, any>): any {
    const name = record.name || record.Name || record.NAME;
    const email = record.email || record.Email || record.EMAIL;
    const phone = record.phone || record.Phone || record.PHONE;

    if (!name || name.trim().length === 0) {
      throw new Error('Name is required');
    }

    if (!email || email.trim().length === 0) {
      throw new Error('Email is required');
    }

    if (!this.isValidEmail(email)) {
      throw new Error(`Invalid email format: ${email}`);
    }

    return {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: phone ? phone.trim().replace(/\D/g, '') : null,
      createdAt: new Date(),
      source: 'bulk_import',
    };
  }

  private async bulkInsertCustomers(customers: any[]): Promise<void> {
    // Simulate bulk database insert
    console.log(`Bulk inserting ${customers.length} customers`);
    await new Promise(resolve => setTimeout(resolve, 10));
  }

  private applyTransformations(record: Record<string, any>, transformations: string[]): any {
    let transformed = { ...record };

    transformations.forEach(transformation => {
      switch (transformation) {
        case 'uppercase_name':
          if (transformed.name) {
            transformed.name = transformed.name.toUpperCase();
          }
          break;
        case 'lowercase_email':
          if (transformed.email) {
            transformed.email = transformed.email.toLowerCase();
          }
          break;
        case 'format_phone':
          if (transformed.phone) {
            transformed.phone = transformed.phone.replace(/\D/g, '');
          }
          break;
        case 'add_timestamp':
          transformed.processedAt = new Date().toISOString();
          break;
      }
    });

    return transformed;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
