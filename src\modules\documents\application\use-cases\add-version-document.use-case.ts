import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { CreateDocumentVersionDto } from '@/modules/documents/infrastructure/dto/create-document-version.dto';
import { DocumentVersion } from '@/modules/documents/domain/entities/document-version.entity';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';

@Injectable()
export class AddDocumentVersionUseCase {
  constructor(
    private readonly prisma: PrismaService,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute(
    uuid: string,
    dto: CreateDocumentVersionDto,
    file: Express.Multer.File,
  ): Promise<DocumentVersion> {
    const document = await this.prisma.document.findUnique({
      where: { id: uuid },
      include: { versions: true },
    });

    if (!document) {
      throw new NotFoundException('Documento não encontrado');
    }

    const newVersionId = document.currentVersion + 1;
    const key = `documents/${uuid}/v${newVersionId}/${file.originalname}`;

    await this.storageProvider.upload(file.buffer, file.mimetype, key);

    const version = await this.prisma.$transaction(async (tx) => {
      const newVersion = await tx.documentVersion.create({
        data: {
          versionId: newVersionId,
          filePath: key,
          uploadedBy: dto.uploadedBy,
          expirationDate: dto.expirationDate
            ? new Date(dto.expirationDate)
            : undefined,
          documentId: document.id,
          uploadedAt: new Date(),
        },
      });

      await tx.document.update({
        where: { id: document.id },
        data: {
          currentVersion: newVersionId,
          updatedAt: new Date(),
          updatedBy: dto.uploadedBy,
        },
      });

      return {
        versionId: newVersion.versionId,
        filePath: newVersion.filePath,
        uploadedAt: newVersion.uploadedAt,
        uploadedBy: newVersion.uploadedBy,
        expirationDate: newVersion.expirationDate || undefined,
      } as DocumentVersion;
    });

    return version;
  }
}
