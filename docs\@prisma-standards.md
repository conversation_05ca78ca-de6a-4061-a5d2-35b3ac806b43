# Prisma & Clean Architecture Pattern Guide

## Objective
This document defines the organization and naming standards for using Prisma ORM in this project, aligned with Clean Architecture and DDD principles. **Follow this pattern in all new features, refactoring, and code reviews.**

---

## 1. Project Structure

```
src/
├── core/                    # Core domain logic and shared functionality
│   ├── domain/             # Domain entities, value objects, and interfaces
│   ├── application/        # Application use cases and services
│   └── infrastructure/     # Infrastructure implementations
├── modules/                # Feature modules
│   └── {module-name}/      # Each module follows the same structure
│       ├── domain/         # Module-specific domain logic
│       │   ├── entities/   # Domain entities
│       │   ├── repositories/ # Repository interfaces
│       │   └── use-cases/  # Use case interfaces
│       ├── application/    # Application layer
│       │   ├── controllers/ # REST controllers
│       │   ├── services/   # Application services
│       │   └── use-cases/  # Use case implementations
│       └── infrastructure/ # Infrastructure implementations
│           ├── repositories/ # Repository implementations
│           └── dtos/       # Data Transfer Objects
└── infrastructure/         # Cross-cutting infrastructure concerns
    ├── prisma/            # Prisma configuration and client
    ├── auth/              # Authentication infrastructure
    ├── swagger/           # Swagger documentation
    │   └── decorators/    # Custom Swagger decorators
    └── common/            # Shared infrastructure code
```

## 2. Domain Table Organization

- **Core Domain:** Use the `core.` prefix for core business tables.
- **Financial Domain:** Use the `finance.` prefix for financial context tables.
- **Cross-cutting/Support Tables:** Use the `core.` prefix for global support tables.
- **Avoid generic tables or tables without domain context.**

## 3. Clean Architecture Layers

### 3.1 Domain Layer
- Contains business entities, value objects, and interfaces
- No dependencies on other layers
- Pure business logic

Example:
```typescript
// domain/entities/user.entity.ts
export interface User {
  id: string;
  email: string;
  name: string;
  // ... other properties
}

// domain/repositories/user.repository.interface.ts
export interface IUserRepository {
  findById(id: string): Promise<User | null>;
  // ... other methods
}
```

### 3.2 Application Layer
- Implements use cases
- Depends only on domain layer
- Orchestrates domain objects

Example:
```typescript
// application/use-cases/user.use-case.ts
@Injectable()
export class UserUseCase implements IUserUseCase {
  constructor(
    @Inject('IUserRepository')
    private readonly userRepository: IUserRepository,
  ) {}
  // ... implementation
}
```

### 3.3 Infrastructure Layer
- Implements interfaces defined in domain layer
- Contains external dependencies (Prisma, HTTP, etc.)

Example:
```typescript
// infrastructure/repositories/user.repository.ts
@Injectable()
export class UserRepository implements IUserRepository {
  constructor(private readonly prisma: PrismaService) {}
  // ... implementation
}
```

## 4. Prisma Schema Standards

### 4.1 Naming Conventions
- Use snake_case for table and field names
- Prefix tables with domain context (e.g., `core.users`, `finance.transactions`)
- Use explicit and self-explanatory names
- Follow the prefix pattern for all domains

### 4.2 Schema Organization
```prisma
// Core domain models
model core_users {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String
  // ... other fields
}

// Financial domain models
model finance_transactions {
  id        String   @id @default(uuid())
  amount    Decimal
  // ... other fields
}
```

## 5. Repository Pattern Implementation

### 5.1 Interface Definition
```typescript
// domain/repositories/user.repository.interface.ts
export interface IUserRepository {
  findById(id: string): Promise<User | null>;
  create(data: CreateUserDto): Promise<User>;
  // ... other methods
}
```

### 5.2 Implementation
```typescript
// infrastructure/repositories/user.repository.ts
@Injectable()
export class UserRepository implements IUserRepository {
  constructor(private readonly prisma: PrismaService) {}
  // ... implementation
}
```

## 6. Swagger Documentation Standards

### 6.1 Organization
- Create dedicated decorator files for each module
- Place all Swagger decorators in `src/infrastructure/swagger/decorators/`
- Use consistent naming conventions for decorators

### 6.2 File Structure
```
src/infrastructure/swagger/decorators/
├── index.ts                # Exports all decorators
├── auth.swagger.ts         # Auth module decorators
├── users.swagger.ts        # Users module decorators
├── company.swagger.ts      # Company module decorators
└── customers.swagger.ts    # Customers module decorators
```

### 6.3 Decorator Implementation
Create reusable, module-specific decorators that apply consistent documentation:

```typescript
// src/infrastructure/swagger/decorators/customers.swagger.ts
export function ApiListCustomers() {
  return applyDecorators(
    ApiOperation({ summary: 'List customers with filtering and pagination' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Customers retrieved successfully',
      type: CustomerListResponseDto,
    }),
    ApiResponse({
      status: HttpStatus.BAD_REQUEST,
      description: 'Invalid pagination parameters',
    }),
  );
}
```

### 6.4 Naming Convention
- Use `Api` prefix for all Swagger decorators
- Follow pattern: `Api{Action}{Entity}` (e.g., `ApiListCustomers`, `ApiCreateUser`)
- Group related operations in the same file

### 6.5 Usage in Controllers
```typescript
// src/modules/customers/application/controllers/customer.controller.ts
import { ApiListCustomers } from '@/infrastructure/swagger/decorators/customers.swagger';

@Get()
@HttpCode(HttpStatus.OK)
@ApiListCustomers()
async listCustomers(@Query() query: CustomerListQueryDto): Promise<CustomerListResponseDto> {
  // Implementation
}
```

### 6.6 Benefits
- Consistent documentation across all endpoints
- Reduced duplication of Swagger decorators
- Improved maintainability and readability
- Better organization of API documentation
- Standardized responses and error handling

## 7. Best Practices

1. **Dependency Rule**: Dependencies should point inward. Outer layers depend on inner layers, not vice versa.
2. **Interface Segregation**: Keep interfaces small and focused.
3. **Single Responsibility**: Each class should have only one reason to change.
4. **Dependency Injection**: Use constructor injection for dependencies.
5. **Error Handling**: Use domain-specific exceptions in the domain layer.
6. **Validation**: Validate data at the boundaries of the application.
7. **Swagger Documentation**: Follow the standardized pattern for all API endpoints.

## 8. References
- [Clean Architecture (Uncle Bob)](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Prisma Naming Conventions](https://www.prisma.io/docs/concepts/components/prisma-schema/naming-conventions)
- [Domain-Driven Design](https://domainlanguage.com/ddd/)
- [NestJS OpenAPI (Swagger)](https://docs.nestjs.com/openapi/introduction)

---

**This pattern must be followed by all team members. In case of doubt, consult this document before creating or modifying Prisma tables or implementing new features.** 