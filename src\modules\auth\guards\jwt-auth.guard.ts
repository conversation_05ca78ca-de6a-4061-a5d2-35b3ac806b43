import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Request } from 'express';

export interface KeycloakIntrospectionResponse {
  active: boolean;
  scope: string;
  client_id: string;
  username: string;
  exp: number;
  iat: number;
  sub: string;
  aud: string;
  iss: string;
  jti: string;
  azp: string;
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [key: string]: {
      roles: string[];
    };
  };
}

interface KeycloakErrorResponse {
  error?: string;
  error_description?: string;
}

@Injectable()
export class JwtAuthGuard implements CanActivate {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private readonly configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('Token não fornecido');
    }

    const token = authHeader.replace('Bearer ', '');

    try {
      const clientId = this.configService.get<string>('KEYCLOAK_CLIENT_ID');
      const clientSecret = this.configService.get<string>(
        'KEYCLOAK_CLIENT_SECRET',
      );
      const baseUrl = this.configService.get<string>('KEYCLOAK_BASE_URL');
      const realm = this.configService.get<string>('KEYCLOAK_REALM');

      if (!clientId || !clientSecret || !baseUrl || !realm) {
        this.logger.error('Configurações do Keycloak incompletas', {
          clientId: !!clientId,
          clientSecret: !!clientSecret,
          baseUrl: !!baseUrl,
          realm: !!realm,
        });
        throw new UnauthorizedException(
          'Configuração de autenticação inválida',
        );
      }

      const params = new URLSearchParams();
      params.append('token', token);
      params.append('client_id', clientId);
      params.append('client_secret', clientSecret);

      const introspectionUrl = `${baseUrl}/realms/${realm}/protocol/openid-connect/token/introspect`;

      try {
        const response = await axios.post<KeycloakIntrospectionResponse>(
          introspectionUrl,
          params,
          {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            timeout: 5000,
          },
        );

        const introspection = response.data;

        if (!introspection.active) {
          this.logger.warn('Token inativo ou expirado', {
            token: `${token.substring(0, 10)}...`,
            active: introspection.active,
            exp: introspection.exp,
          });
          throw new UnauthorizedException('Token inválido ou expirado');
        }

        request.user = {
          ...introspection,
          id: introspection.sub,
          email: introspection.username,
        };

        return true;
      } catch (axiosError: unknown) {
        if (axios.isAxiosError(axiosError)) {
          const errorData = axiosError.response?.data as
            | KeycloakErrorResponse
            | undefined;
          const status = axiosError.response?.status;

          this.logger.error('Erro na validação do token com Keycloak', {
            status,
            statusText: axiosError.response?.statusText,
            error: errorData,
            url: axiosError.config?.url,
            clientId,
            realm,
          });

          if (status === 401) {
            throw new UnauthorizedException(
              'Credenciais inválidas. Verifique o client_id e client_secret.',
            );
          }
          if (status === 403) {
            throw new UnauthorizedException(
              'Acesso negado. Verifique as permissões do cliente no Keycloak.',
            );
          }
          if (status === 404) {
            throw new UnauthorizedException(
              'Endpoint não encontrado. Verifique a URL do Keycloak e o realm.',
            );
          }

          const errorMessage =
            errorData?.error_description ||
            errorData?.error ||
            'Falha na validação do token';
          throw new UnauthorizedException(errorMessage);
        }
        throw axiosError;
      }
    } catch {
      throw new UnauthorizedException('Falha na validação do token');
    }
  }
}
