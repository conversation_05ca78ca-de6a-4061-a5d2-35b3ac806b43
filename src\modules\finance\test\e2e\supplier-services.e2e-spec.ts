import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../../app.module';
import { v4 as uuidv4 } from 'uuid';
import { fail } from 'assert';
import { ConfigModule } from '@nestjs/config';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { KeycloakIdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';

dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });
jest.setTimeout(120000);

// Utilitário para criar supplier e obter token
async function createSupplierAndToken(app: INestApplication) {
  const testEmail = `e2e-services-${uuidv4().substring(0, 8)}@example.com`;
  const supplierEmail = `e2e-supplier-services-${uuidv4().substring(0, 8)}@example.com`;
  const testPassword = 'Test@123456';

  // Registrar usuário
  const registerResponse = await request(app.getHttpServer())
    .post('/auth/register')
    .send({
      email: testEmail,
      password: testPassword,
      name: 'E2E Services User',
      type: 'EMPLOYEE',
      cpf: '12345678901',
    });
  if (registerResponse.status !== 201) {
    fail(`Registration failed: ${registerResponse.status}`);
  }
  const registerAuthResponse = registerResponse.body;
  let keycloakUserId =
    registerAuthResponse.user?.keycloakId ||
    registerAuthResponse.user?.sub ||
    '';

  // Fallback: login e getUserInfo
  let accessToken: string | undefined;
  if (!keycloakUserId) {
    const tempLoginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ username: testEmail, password: testPassword });
    if (tempLoginResponse.status === 200) {
      accessToken = tempLoginResponse.body.access_token;
      if (accessToken) {
        const keycloakIdentityProviderService = app.get(KeycloakIdentityProviderService);
        try {
          const userInfo = await keycloakIdentityProviderService.getUserInfo(accessToken);
          keycloakUserId = userInfo.id;
        } catch (error) {
          // Continua sem keycloakUserId
        }
      }
    }
  }

  // Atribuir papel ADMIN
  if (keycloakUserId) {
    const keycloakIdentityProviderService = app.get(KeycloakIdentityProviderService);
    try {
      await keycloakIdentityProviderService.assignUserRoles(keycloakUserId, ['ADMIN']);
      await new Promise((resolve) => setTimeout(resolve, 5000));
    } catch (error) {
      // Continua mesmo se falhar
    }
  }

  // Login final para garantir token com role
  const loginResponse = await request(app.getHttpServer())
    .post('/auth/login')
    .send({ username: testEmail, password: testPassword });
  if (loginResponse.status !== 200) {
    fail(`Login failed: ${loginResponse.status}`);
  }
  accessToken = loginResponse.body.access_token;

  // Criar supplier
  const createDto = {
    name: 'E2E Supplier Services',
    document: `${Math.floor(Math.random() * 1e14)}`.padStart(14, '1'),
    tradeName: 'E2E Services Trade Name',
    address: {
      street: 'Rua Teste',
      city: 'Cidade Teste',
      zipCode: '12345-678',
      state: 'TS',
    },
    type: 'BANK',
    email: supplierEmail,
  };
  const supplierResponse = await request(app.getHttpServer())
    .post('/core/suppliers')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(createDto);
  if (supplierResponse.status !== 201) {
    fail(`Supplier creation failed: ${supplierResponse.status}`);
  }
  const createdSupplierId = supplierResponse.body.id || supplierResponse.body.uuid;
  return { accessToken, createdSupplierId };
}

describe('Supplier Services (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const mockStorageProvider: IStorageProvider = {
      upload: jest.fn().mockResolvedValue(undefined),
      getDownloadUrl: jest.fn().mockResolvedValue('https://fake-url.com/service-e2e.pdf'),
      getFileUrl: jest.fn().mockResolvedValue('https://fake-url.com/service-e2e.pdf'),
      getFileStream: jest.fn().mockResolvedValue({}),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          envFilePath: path.resolve(process.cwd(), '.env.test'),
          isGlobal: true,
        }),
        AppModule,
      ],
    })
      .overrideProvider('IStorageProvider')
      .useValue(mockStorageProvider)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        skipMissingProperties: false,
      }),
    );
    await app.init();
  });

  afterAll(async () => {
    if (app) await app.close();
  });

  it('should create and list supplier services', async () => {
    const { accessToken, createdSupplierId } = await createSupplierAndToken(app);
    // POST services
    const servicesPayload = [
      {
        type: 'Consultoria',
        rate: 'R$ 150/hora',
        description: 'Consultoria em TI para projetos e arquitetura',
      },
      {
        type: 'Desenvolvimento',
        rate: 'R$ 100/hora',
        description: 'Desenvolvimento de software sob demanda',
      },
    ];
    const postResponse = await request(app.getHttpServer())
      .post(`/core/suppliers/${createdSupplierId}/services`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(servicesPayload);
    expect(postResponse.status).toBe(201);
    expect(Array.isArray(postResponse.body)).toBe(true);
    expect(postResponse.body.length).toBe(2);
    for (const [i, service] of postResponse.body.entries()) {
      expect(service).toHaveProperty('id');
      expect(service).toHaveProperty('entityType', 'SUPPLIER');
      expect(service).toHaveProperty('entityUuid', createdSupplierId);
      expect(service).toHaveProperty('type', servicesPayload[i].type);
      expect(service).toHaveProperty('rate', servicesPayload[i].rate);
      expect(service).toHaveProperty('description', servicesPayload[i].description);
      expect(service).toHaveProperty('createdAt');
      expect(service).toHaveProperty('createdBy');
      expect(service).toHaveProperty('updatedAt');
      expect(service).toHaveProperty('updatedBy');
    }
    // GET services
    const getResponse = await request(app.getHttpServer())
      .get(`/core/suppliers/${createdSupplierId}/services`)
      .set('Authorization', `Bearer ${accessToken}`);
    expect(getResponse.status).toBe(200);
    expect(Array.isArray(getResponse.body)).toBe(true);
    expect(getResponse.body.length).toBeGreaterThanOrEqual(2);
    for (const service of servicesPayload) {
      const found = getResponse.body.find((s: any) => s.type === service.type && s.rate === service.rate);
      expect(found).toBeDefined();
      expect(found).toHaveProperty('entityType', 'SUPPLIER');
      expect(found).toHaveProperty('entityUuid', createdSupplierId);
      expect(found).toHaveProperty('description', service.description);
    }
  });
}); 