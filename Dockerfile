FROM node:20-alpine AS builder

# Set the working directory
WORKDIR /app

# Copia arquivos de dependência
COPY package*.json ./

# Instala dependências
RUN npm install -g npm && npm install

# Copia código fonte
COPY . .

# Gera o cliente Prisma e compila o código
RUN npx prisma generate

# Stage development: Prepare the runtime image
FROM node:22-alpine AS development

# Set the working directory
WORKDIR /app

# Copy the built application from the builder stage
COPY --from=builder /app /app

# Run the application using pnpm
CMD ["sh", "-c", "npx prisma migrate deploy && npm run start"] 

# Stage de produção
FROM node:18-alpine AS production

WORKDIR /app

# Define argumentos e variáveis de ambiente
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

# Copia apenas o necessário para produção
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/prisma ./prisma

COPY --from=builder /app /app


# Build the application
RUN npm run build

# Define o ponto de entrada
CMD ["sh", "-c", "npx prisma migrate deploy && npm run start:prod"] 