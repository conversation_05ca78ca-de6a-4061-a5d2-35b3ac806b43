import {
  Address,
  Dependent,
  Employee,
  PersonalDocument,
} from '@/core/domain/entities/employee.entity';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import { UpdateEmployeeDto } from '@/modules/finance/employee/dto/update-employee.dto';
import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { EmployeeStatus } from '@prisma/client';

@Injectable()
export class UpdateEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
  ) {}

  async execute(
    id: string,
    updateData: UpdateEmployeeDto,
    userId: string,
  ): Promise<Employee> {
    const existingEmployee = await this.employeeRepository.findByUuid(id);

    if (!existingEmployee) {
      throw new NotFoundException('Colaborador não encontrado.');
    }

    const existingEmployeeByEmail =
      updateData.email && updateData.email !== existingEmployee.email
        ? await this.employeeRepository.findByEmail(updateData.email)
        : null;

    if (
      existingEmployeeByEmail &&
      existingEmployeeByEmail.uuid !== existingEmployee.uuid
    ) {
      throw new ConflictException({
        code: 'Email já existente',
        message: 'O email do funcionário já existe',
      });
    }

    const address = {
      street: updateData.address?.street ?? existingEmployee.address.street,
      number: updateData.address?.number ?? existingEmployee.address.number,
      neighborhood:
        updateData.address?.neighborhood ??
        existingEmployee.address.neighborhood,
      complement:
        updateData.address?.complement ?? existingEmployee.address.complement,
      city: updateData.address?.city ?? existingEmployee.address.city,
      state: updateData.address?.state ?? existingEmployee.address.state,
      zipCode: updateData.address?.zipCode ?? existingEmployee.address.zipCode,
    };

    const personalDocuments =
      updateData.personalDocuments?.map((doc) => ({
        type: doc.type ?? '',
        number: doc.number ?? '',
        issuingAgency: doc.issuingAgency,
        issueDate: doc.issueDate,
      })) ?? existingEmployee.personalDocuments;
    const dependents =
      updateData.dependents?.map((dep) => ({
        name: dep.name ?? '',
        kinship: dep.kinship ?? '',
        birthDate: dep.birthDate,
        isTaxDependent: dep.isTaxDependent,
        hasHealthPlan: dep.hasHealthPlan,
      })) ?? existingEmployee.dependents;

    const updatedEmployee = Employee.create(
      existingEmployee.id,
      existingEmployee.uuid,
      updateData.name ?? existingEmployee.name,
      updateData.email ?? existingEmployee.email,
      updateData.position ?? existingEmployee.position,
      updateData.department ?? existingEmployee.department,
      updateData.hireDate
        ? new Date(updateData.hireDate)
        : existingEmployee.hireDate,
      address,
      personalDocuments,
      dependents,
      updateData.status ?? existingEmployee.status,
      existingEmployee.createdBy,
      userId,
      existingEmployee.createdAt,
      new Date(),
      updateData.workSchedule ?? existingEmployee.workSchedule,
      updateData.shift ?? existingEmployee.shift,
      updateData.grossSalary ?? existingEmployee.grossSalary,
      updateData.mealAllowance ?? existingEmployee.mealAllowance,
      updateData.transportAllowance ?? existingEmployee.transportAllowance,
      updateData.healthPlan ?? existingEmployee.healthPlan,
      updateData.contractType ?? existingEmployee.contractType,
      updateData.seniority ?? existingEmployee.seniority,
      updateData.phone ?? existingEmployee.phone,
      updateData.birthDate
        ? new Date(updateData.birthDate)
        : existingEmployee.birthDate,
      updateData.workHours ?? existingEmployee.workHours,
      updateData.overtimeBank ?? existingEmployee.overtimeBank,
      updateData.vacations ?? existingEmployee.vacations,
    );

    return this.employeeRepository.update(updatedEmployee);
  }
}
