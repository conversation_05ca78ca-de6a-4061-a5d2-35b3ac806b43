import { ApiProperty } from '@nestjs/swagger';
import { PaymentMethodResponseDto } from './payment-mehtod.response.dto';

export class PaymentMethodsResponseDto {
  @ApiProperty({ type: [PaymentMethodResponseDto] })
  items: PaymentMethodResponseDto[];

  @ApiProperty({ description: 'Total number of items' })
  total: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Number of items skipped' })
  offset: number;
}
