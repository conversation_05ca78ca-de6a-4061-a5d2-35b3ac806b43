/**
 * Exemplo de integração do FileReaderModule com outros módulos
 * 
 * Este arquivo mostra como integrar o FileReaderModule em diferentes cenários
 */

// 1. Integração básica no app.module.ts
/*
import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { FileReaderModule } from './modules/file-reader';
import { fileUploadConfig } from './modules/file-reader/config/multer.config';

@Module({
  imports: [
    // Configuração global do Multer
    MulterModule.register(fileUploadConfig),
    
    // Importar o FileReaderModule
    FileReaderModule,
    
    // Outros módulos...
  ],
})
export class AppModule {}
*/

// 2. Integração em um módulo específico (ex: CustomersModule)
/*
import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { FileReaderModule } from '../file-reader';
import { csvOnlyConfig } from '../file-reader/config/multer.config';
import { CustomersService } from './customers.service';
import { CustomersController } from './customers.controller';
import { CustomersImportController } from './customers-import.controller';

@Module({
  imports: [
    // Configuração específica para CSV apenas
    MulterModule.register(csvOnlyConfig),
    FileReaderModule,
  ],
  controllers: [
    CustomersController,
    CustomersImportController,
  ],
  providers: [CustomersService],
})
export class CustomersModule {}
*/

// 3. Exemplo de serviço que usa o FileReaderService
/*
import { Injectable, BadRequestException } from '@nestjs/common';
import { FileReaderService, FileReaderOptions } from '../file-reader';

@Injectable()
export class CustomersImportService {
  constructor(private readonly fileReaderService: FileReaderService) {}

  async importCustomersFromFile(file: Express.Multer.File) {
    const options: FileReaderOptions = {
      requiredColumns: ['name', 'email', 'phone'],
      trimValues: true,
      skipEmptyLines: true,
    };

    try {
      const result = await this.fileReaderService.readFile(file, options);
      
      // Processar os dados
      const customers = result.data.map(row => ({
        name: row.name,
        email: row.email.toLowerCase(),
        phone: row.phone.replace(/\D/g, ''),
        createdAt: new Date(),
      }));

      // Salvar no banco de dados
      // await this.customersRepository.bulkCreate(customers);

      return {
        imported: customers.length,
        total: result.totalRows,
        errors: result.errors || [],
      };
    } catch (error) {
      throw new BadRequestException(`Import failed: ${error.message}`);
    }
  }

  async importLargeCustomerFile(file: Express.Multer.File) {
    const options: FileReaderOptions = {
      requiredColumns: ['name', 'email'],
      trimValues: true,
      skipEmptyLines: true,
    };

    let totalImported = 0;
    const errors: string[] = [];

    try {
      await this.fileReaderService.processFileInBatches(
        file,
        options,
        1000, // Processar 1000 registros por vez
        async (batch, batchNumber) => {
          console.log(`Processing batch ${batchNumber} with ${batch.length} records`);
          
          const validCustomers = [];
          
          batch.forEach((row, index) => {
            try {
              if (!this.isValidEmail(row.email)) {
                throw new Error(`Invalid email: ${row.email}`);
              }
              
              validCustomers.push({
                name: row.name,
                email: row.email.toLowerCase(),
                phone: row.phone || null,
                createdAt: new Date(),
              });
            } catch (error) {
              errors.push(`Batch ${batchNumber}, Row ${index + 1}: ${error.message}`);
            }
          });

          if (validCustomers.length > 0) {
            // await this.customersRepository.bulkCreate(validCustomers);
            totalImported += validCustomers.length;
          }
        }
      );

      return {
        imported: totalImported,
        errors,
      };
    } catch (error) {
      throw new BadRequestException(`Batch import failed: ${error.message}`);
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
*/

// 4. Exemplo de controlador com diferentes endpoints
/*
import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiConsumes, ApiOperation } from '@nestjs/swagger';
import { CustomersImportService } from './customers-import.service';

@ApiTags('Customers Import')
@Controller('customers/import')
export class CustomersImportController {
  constructor(
    private readonly customersImportService: CustomersImportService,
  ) {}

  @Post('csv')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import customers from CSV file' })
  async importFromCsv(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return await this.customersImportService.importCustomersFromFile(file);
  }

  @Post('excel')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import customers from Excel file' })
  async importFromExcel(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return await this.customersImportService.importCustomersFromFile(file);
  }

  @Post('large-file')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import customers from large file using batch processing' })
  async importLargeFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return await this.customersImportService.importLargeCustomerFile(file);
  }

  @Post('validate')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Validate file before import' })
  async validateFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Usar o método getFileInfo para validação rápida
    const fileInfo = await this.fileReaderService.getFileInfo(file);
    
    const requiredColumns = ['name', 'email'];
    const missingColumns = requiredColumns.filter(
      col => !fileInfo.columns?.includes(col)
    );

    return {
      filename: fileInfo.filename,
      size: fileInfo.size,
      type: fileInfo.type,
      columns: fileInfo.columns,
      isValid: missingColumns.length === 0,
      missingColumns,
    };
  }
}
*/

// 5. Exemplo de middleware personalizado para validação de arquivos
/*
import { Injectable, NestMiddleware, BadRequestException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class FileValidationMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Validações customizadas antes do upload
    const contentType = req.headers['content-type'];
    
    if (!contentType || !contentType.includes('multipart/form-data')) {
      throw new BadRequestException('Content-Type must be multipart/form-data');
    }

    // Verificar tamanho do arquivo no header (se disponível)
    const contentLength = req.headers['content-length'];
    if (contentLength && parseInt(contentLength) > 50 * 1024 * 1024) {
      throw new BadRequestException('File too large. Maximum size is 50MB');
    }

    next();
  }
}
*/

export {}; // Para tornar este arquivo um módulo TypeScript
