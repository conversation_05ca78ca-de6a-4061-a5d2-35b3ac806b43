/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

export function omitSensitiveFields(body: any): any {
  if (!body || typeof body !== 'object') return body;
  const sensitiveFields = [
    'password',
    'newPassowrd',
    'token',
    'cpf',
    'cnpj',
    'creditCardNumber',
    'securityCode',
    'expirationDate',
  ];
  return Object.keys(body).reduce((acc, key) => {
    acc[key] = sensitiveFields.includes(key) ? '[REDACTED]' : body[key];
    return acc;
  }, {});
}
