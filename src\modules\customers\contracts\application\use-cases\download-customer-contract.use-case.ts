import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { ICustomerContractRepository } from '../../domain/repositories/customer-contract.repository.interface';

@Injectable()
export class DownloadCustomerContractUseCase {
  constructor(
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
    @Inject('ICustomerContractRepository')
    private readonly contractRepository: ICustomerContractRepository,
  ) {}

  async execute(customerUuid: string, contractUuid: string, expiresIn: number = 3600): Promise<{ downloadUrl: string; fileName: string }> {
    const contract = await this.contractRepository.findByContractUuid(contractUuid);
    
    if (!contract || contract.customerUuid !== customerUuid) {
      throw new NotFoundException('Contrato não encontrado');
    }

    // O padrão de chave no S3 é: customer/{customerUuid}/{contractUuid}/v{version}/{fileName}
    const key = contract.url; // Assumindo que o URL armazena a chave S3
    
    // Extrair o fileName real do filePath
    let fileName = contract.fileName;
    if (!fileName && key) {
      const parts = key.split('/');
      fileName = parts.length > 0 ? parts[parts.length - 1] : 'contrato.pdf';
    }
    fileName = fileName || 'contrato.pdf';
    
    const downloadUrl = await this.storageProvider.getDownloadUrl(key, fileName, expiresIn);
    
    return {
      downloadUrl,
      fileName,
    };
  }

  async executeMultiple(customerUuid: string, contractUuids: string[], expiresIn: number = 3600): Promise<Array<{ contractUuid: string; downloadUrl: string; fileName: string }>> {
    const results = await Promise.all(
      contractUuids.map(async (contractUuid) => {
        try {
          const result = await this.execute(customerUuid, contractUuid, expiresIn);
          return {
            contractUuid,
            ...result,
          };
        } catch (error) {
          return {
            contractUuid,
            downloadUrl: '',
            fileName: '',
          };
        }
      })
    );

    return results;
  }
} 