import { Test, TestingModule } from '@nestjs/testing';
import { UpdateCustomerUseCase } from './update-customer.use-case';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { NotFoundException } from '@nestjs/common';
import { CustomerStatus} from '../../domain/entities/customer.entity';
import { DuplicateCnpjError } from '@/infrastructure/exceptions/duplicate-cnpj.error';

describe('UpdateCustomerUseCase', () => {
  let useCase: UpdateCustomerUseCase;
  let customerRepository: jest.Mocked<ICustomerRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateCustomerUseCase,
        {
          provide: 'ICustomerRepository',
          useValue: {
            findByUuid: jest.fn(),
            findByDocument: jest.fn(),
            findByEmail: jest.fn(),
            update: jest.fn(),
          },
        },
      ],
    }).compile();
    useCase = module.get(UpdateCustomerUseCase);
    customerRepository = module.get('ICustomerRepository');
  });

  it('should update customer if found and no document conflict', async () => {
    customerRepository.findByUuid.mockResolvedValue({
      uuid: '123',
      cnpj: 'doc',
      razaoSocial: 'Test',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    customerRepository.findByDocument.mockResolvedValue(null);
    customerRepository.findByEmail.mockResolvedValue(null);
    customerRepository.update.mockResolvedValue({
      uuid: '123',
      cnpj: 'newdoc',
      razaoSocial: 'Test',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    const result = await useCase.execute({
      uuid: '123',
      data: { cnpj: 'newdoc' },
      updatedBy: 'admin',
    });
    expect(result.cnpj).toBe('newdoc');
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.update).toHaveBeenCalled();
  });

  it('should throw NotFoundException if customer not found', async () => {
    customerRepository.findByUuid.mockResolvedValue(null);
    await expect(
      useCase.execute({ uuid: 'notfound', data: {}, updatedBy: 'admin' }),
    ).rejects.toThrow(NotFoundException);
  });

  it('should throw DuplicateCnpjError if document already exists', async () => {
    customerRepository.findByUuid.mockResolvedValue({
      uuid: '123',
      cnpj: 'doc',
      razaoSocial: 'Test',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    customerRepository.findByDocument.mockResolvedValue({
      uuid: 'other',
      cnpj: 'newdoc',
      razaoSocial: 'Other',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user2',
      url: 'https://other.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    await expect(
      useCase.execute({
        uuid: '123',
        data: { cnpj: 'newdoc' },
        updatedBy: 'admin',
      }),
    ).rejects.toThrow(DuplicateCnpjError);
  });
});
