import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.test' });
import { KeycloakService } from '../../keycloak.service';
import { KeycloakAdminUtils } from '../../keycloak.admin.utils';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../utils/request.utils.service';

describe('Keycloak Integration', () => {
  let configService: ConfigService;
  let requestUtilsService: RequestUtilsService;
  let keycloakAdminUtils: KeycloakAdminUtils;
  let keycloakService: KeycloakService;

  beforeAll(() => {
    configService = {
      get: jest.fn((key: string) => process.env[key]),
    } as unknown as ConfigService;
    requestUtilsService = {
      executeWithRetry: jest.fn((fn: () => unknown) => fn()),
    } as unknown as RequestUtilsService;
    keycloakAdminUtils = {
      getAdminToken: jest.fn().mockResolvedValue('mock-admin-token'),
      createUser: jest.fn(),
      assignRole: jest.fn(),
    } as unknown as KeycloakAdminUtils;
    keycloakService = new KeycloakService(
      {} as Record<string, unknown>,
      keycloakAdminUtils,
      configService,
      requestUtilsService,
    );
  });

  it('should instantiate services for integration', () => {
    expect(keycloakService).toBeDefined();
    expect(keycloakAdminUtils).toBeDefined();
  });

  it('should create and authenticate a user (mocked)', async () => {
    const userId = 'mock-user-id';
    (keycloakAdminUtils.createUser as jest.Mock).mockResolvedValue(userId);
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => ({
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        expires_in: 3600,
        token_type: 'bearer',
      }),
    );
    const createdUserId = await keycloakService.createUser({
      username: 'test',
      email: '<EMAIL>',
      password: '123456',
    });
    expect(createdUserId).toBe(userId);
    const tokenResult = await keycloakService.token('test', '123456');
    expect(tokenResult.access_token).toBe('mock-token');
    expect(tokenResult.refresh_token).toBe('mock-refresh');
  });

  it('should assign a role to a user (mocked)', async () => {
    (keycloakAdminUtils.assignRole as jest.Mock).mockResolvedValue(undefined);
    const assignRoleMock = jest.spyOn(keycloakAdminUtils, 'assignRole');
    await expect(
      keycloakService.assignRole('mock-user-id', 'admin'),
    ).resolves.toBeUndefined();
    expect(assignRoleMock).toHaveBeenCalledWith(
      'mock-admin-token',
      'mock-user-id',
      'admin',
    );
  });

  it('should refresh a token (mocked)', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => ({ access_token: 'new-token' }),
    );
    const result = await keycloakService.refreshToken('mock-refresh');
    expect(result.access_token).toBe('new-token');
  });

  it('should handle error when user not found (mocked)', async () => {
    (keycloakAdminUtils.createUser as jest.Mock).mockRejectedValue(
      new Error('User not found'),
    );
    await expect(
      keycloakService.createUser({
        username: 'notfound',
        email: '<EMAIL>',
        password: '123456',
      }),
    ).rejects.toThrow('User not found');
  });
});
