import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class ListSectorsQueryDto {
  @ApiProperty({
    description: 'Number of items per page',
    example: 5,
    minimum: 1,
    required: false,
    default: 10,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({
    description: 'Number of items to skip',
    example: 0,
    minimum: 0,
    required: false,
    default: 0,
  })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  offset?: number = 0;

  @ApiProperty({
    description: 'Exact code to filter sectors',
    example: 'HR',
    required: false,
  })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({
    description: 'Partial description to filter sectors',
    example: 'Recursos',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
