import { Contact } from './contact.value-object';

describe('Contact Value Object', () => {
  const validContact = {
    email: '<EMAIL>',
    phone: '(11) 99999-9999',
  };

  describe('constructor', () => {
    it('should create a valid contact', () => {
      const contact = new Contact(validContact.email, validContact.phone);

      expect(contact.email).toBe(validContact.email);
      expect(contact.phone).toBe(validContact.phone);
    });

    it('should throw error when email is empty', () => {
      expect(() => {
        new Contact('', validContact.phone);
      }).toThrow('Email is required');
    });

    it('should throw error when phone is empty', () => {
      expect(() => {
        new Contact(validContact.email, '');
      }).toThrow('Phone is required');
    });

    it('should throw error when email format is invalid', () => {
      const invalidEmails = [
        'test', // Sem @ e domínio
        'test@', // Sem domínio
        'test@example', // Sem TLD
        '@example.com', // Sem nome de usuário
        'test <EMAIL>', // Com espaço
        'test@example@com', // Múltiplos @
      ];

      invalidEmails.forEach((invalidEmail) => {
        expect(() => {
          new Contact(invalidEmail, validContact.phone);
        }).toThrow('Invalid email format');
      });
    });

    it('should throw error when phone format is invalid', () => {
      const invalidPhones = [
        '11999999999', // Sem formatação
        '(11)99999-9999', // Sem espaço após parênteses
        '(11) 999999999', // Sem hífen
        '(11) 999-9999', // Formato errado
        '11 99999-9999', // Sem parênteses
        '(111) 99999-9999', // DDD com 3 dígitos
        '(11) 99999-99999', // Número com dígitos a mais
      ];

      invalidPhones.forEach((invalidPhone) => {
        expect(() => {
          new Contact(validContact.email, invalidPhone);
        }).toThrow(
          'Invalid phone format. Use: (XX) XXXX-XXXX or (XX) XXXXX-XXXX',
        );
      });
    });

    it('should accept both 8 and 9 digit phone numbers', () => {
      const validPhones = [
        '(11) 9999-9999', // 8 dígitos
        '(11) 99999-9999', // 9 dígitos
      ];

      validPhones.forEach((validPhone) => {
        expect(() => {
          new Contact(validContact.email, validPhone);
        }).not.toThrow();
      });
    });
  });

  describe('toJSON', () => {
    it('should return contact as JSON object', () => {
      const contact = new Contact(validContact.email, validContact.phone);

      const json = contact.toJSON();

      expect(json).toEqual({
        email: validContact.email,
        phone: validContact.phone,
      });
    });
  });

  describe('getters', () => {
    it('should return correct values through getters', () => {
      const contact = new Contact(validContact.email, validContact.phone);

      expect(contact.email).toBe(validContact.email);
      expect(contact.phone).toBe(validContact.phone);
    });
  });
});
